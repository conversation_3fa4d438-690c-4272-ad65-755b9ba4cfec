#pragma once

#include <stdint.h>
#include "esp_err.h"
#include "general_mocks.h"

#define tskNO_AFFINITY 1
#define ESP_EVENT_ANY_BASE     NULL             /**< register handler for any event base */
#define ESP_EVENT_ANY_ID       -1               /**< register handler for any event id */

typedef unsigned int	UBaseType_t;
typedef int	BaseType_t;
typedef uint32_t TickType_t;

typedef struct {
    int32_t queue_size;                         /**< size of the event loop queue */
    const char *task_name;                      /**< name of the event loop task; if NULL,
                                                        a dedicated task is not created for event loop*/
    UBaseType_t task_priority;                  /**< priority of the event loop task, ignored if task name is NULL */
    uint32_t task_stack_size;                   /**< stack size of the event loop task, ignored if task name is NULL */
    BaseType_t task_core_id;                    /**< core to which the event loop task is pinned to,
                                                        ignored if task name is NULL */
} esp_event_loop_args_t;

typedef void *esp_event_loop_handle_t;
typedef const void * esp_event_base_t;

typedef void         (*esp_event_handler_t)(void* event_handler_arg,
                                        esp_event_base_t event_base,
                                        int32_t event_id,
                                        void* event_data);

// Defines for declaring and defining event base
#define ESP_EVENT_DECLARE_BASE(id) extern esp_event_base_t id
#define ESP_EVENT_DEFINE_BASE(id) esp_event_base_t id = #id




esp_err_t esp_event_loop_create(esp_event_loop_args_t *event_loop_args, 
                              esp_event_loop_handle_t *event_loop);

esp_err_t esp_event_handler_register_with(esp_event_loop_handle_t event_loop, esp_event_base_t event_base,
                                        int32_t event_id, esp_event_handler_t event_handler, void* event_handler_arg);

esp_err_t esp_event_post_to(esp_event_loop_handle_t event_loop, 
                            esp_event_base_t event_base, 
                            int32_t event_id, 
                            void *event_data, 
                            size_t event_data_size, 
                            TickType_t ticks_to_wait);


UBaseType_t uxTaskPriorityGet(void *xTask);


/**
 *        mock tools
 */
void clear_esp_event_mock();
void print_event_arrays();
void print_CB_array();