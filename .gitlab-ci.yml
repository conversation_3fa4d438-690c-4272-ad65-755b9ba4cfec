---

stages:
  - test
  - build

workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: $CI_COMMIT_BRANCH
    - if: $CI_COMMIT_TAG

variables:
  GIT_SUBMODULE_STRATEGY: recursive

test:stage:unit_test1:
  stage: test
  image:
    name: jwgrenning/cpputest-runner
    entrypoint: [ "" ]
  script:
    - make -C main/_BSH_system/__unit-test/esp_mocks

test:stage:unit_test2:
  stage: test
  image:
    name: jwgrenning/cpputest-runner
    entrypoint: [ "" ]
  script:
    - make -C main/_BSH_system/__unit-test

test:stage:unit_test3:
  stage: test
  image:
    name: jwgrenning/cpputest-runner
    entrypoint: [ "" ]
  script:
    - make -C main/__unit-test

test:stage:unit_test4:
  stage: test
  image:
    name: jwgrenning/cpputest-runner
    entrypoint: [ "" ]
  script:
    - make -C main/_BSH_system/__unit-test/_unit_tests_group_2


build:stage:
  rules:
    # - if: '$CI_COMMIT_REF_NAME == "master" || $CI_COMMIT_REF_NAME == "develop"'
    - when: manual
  stage: build
  image:
    name: espressif/idf:release-v4.4
    entrypoint: [ "" ]
  script:
    # building debug version
    - /opt/esp/entrypoint.sh idf.py build
    - cd build
    - /opt/esp/entrypoint.sh esptool.py --chip esp32-s3 merge_bin -o merged_flash_image.bin @flash_args
    # copying artifacts
    - cd ..
    - mkdir target-s3-debug
    - cp ./build/A630.bin ./target-s3-debug/a30.bin
    - cp ./build/ota_data_initial.bin ./target-s3-debug/ota_data_initial.bin
    - cp ./build/bootloader/bootloader.bin ./target-s3-debug/bootloader.bin
    - cp ./build/partition_table/partition-table.bin ./target-s3-debug/partition-table.bin
    - cp ./build/merged_flash_image.bin ./target-s3-debug/merged_flash_image.bin
    - cp ./flashing_help.txt ./target-s3-debug/flashing_help.txt



    # building release version
    - mv sdkconfig sdkconfig_tmp2
    - mv sdkconfig_log_level_ERR sdkconfig
    - /opt/esp/entrypoint.sh idf.py fullclean
    - /opt/esp/entrypoint.sh idf.py build
    - cd build
    - /opt/esp/entrypoint.sh esptool.py --chip esp32-s3 merge_bin -o merged_flash_image.bin @flash_args
    # copying artifacts
    - cd ..
    - mkdir target_s3_release
    - cp ./build/A630.bin ./target_s3_release/A630.bin
    - cp ./build/ota_data_initial.bin ./target_s3_release/ota_data_initial.bin
    - cp ./build/bootloader/bootloader.bin ./target_s3_release/bootloader.bin
    - cp ./build/partition_table/partition-table.bin ./target_s3_release/partition-table.bin
    - cp ./build/merged_flash_image.bin ./target_s3_release/merged_flash_image.bin
    - cp ./flashing_help.txt ./target_s3_release/flashing_help.txt
    - mv sdkconfig sdkconfig_log_level_ERR
    - mv sdkconfig_tmp2 sdkconfig


    # building esp32 dev board version
    - mv sdkconfig sdkconfig_tmp
    - mv sdkconfig_for_esp32 sdkconfig
    - /opt/esp/entrypoint.sh idf.py fullclean
    - /opt/esp/entrypoint.sh idf.py build
    - cd build
    - /opt/esp/entrypoint.sh esptool.py --chip esp32-s3 merge_bin -o merged_flash_image.bin @flash_args
    # copying artifacts
    - cd ..
    - mkdir for_esp32_development_board
    - cp ./build/A630.bin ./for_esp32_development_board/A630.bin
    - cp ./build/ota_data_initial.bin ./for_esp32_development_board/ota_data_initial.bin
    - cp ./build/bootloader/bootloader.bin ./for_esp32_development_board/bootloader.bin
    - cp ./build/partition_table/partition-table.bin ./for_esp32_development_board/partition-table.bin
    - cp ./build/merged_flash_image.bin ./for_esp32_development_board/merged_flash_image.bin
    - cp ./flashing_help_dev_board.txt ./for_esp32_development_board/flashing_help_dev_board.txt
    - mv sdkconfig sdkconfig_for_esp32
    - mv sdkconfig_tmp sdkconfig


    # firmware version
    - VER_TEXT=$"firmware_version"
    - VER_MAJOR=$(grep FIRMWARE_VERSION_MAJOR ./main/device_specific_data.h | cut -d ' ' -f 3)
    - VER_MINOR=$(grep FIRMWARE_VERSION_MINOR ./main/device_specific_data.h | cut -d ' ' -f 3)
    - VER_PATCH=$(grep FIRMWARE_VERSION_PATCH ./main/device_specific_data.h | cut -d ' ' -f 3)
    - FIRM_VER="${VER_TEXT}_${VER_MAJOR}_${VER_MINOR}_${VER_PATCH}"
    - touch ./target-s3-debug/$FIRM_VER
    - touch ./target_s3_release/$FIRM_VER
    - touch ./for_esp32_development_board/$FIRM_VER

  dependencies:
    - test:stage:unit_test1
    - test:stage:unit_test2
    - test:stage:unit_test3
    - test:stage:unit_test4
  artifacts:
    paths:
      - target-s3-debug/*
      - target_s3_release/*
      - for_esp32_development_board/*