test-obj/../mqtt_logs/mqtt_logs.o: ../mqtt_logs/mqtt_logs.cpp \
 /opt/cpputest/include/CppUTest/MemoryLeakDetectorNewMacros.h \
 /opt/cpputest/include/CppUTest/CppUTestConfig.h \
 /opt/cpputest/include/CppUTest/CppUTestGeneratedConfig.h \
 /opt/cpputest/include/CppUTest/StandardCLibrary.h \
 /opt/cpputest/include/CppUTest/MemoryLeakDetectorMallocMacros.h \
 esp_mocks/esp_log.h esp_mocks/nvs.h esp_mocks/esp_err.h \
 esp_mocks/esp_timer.h esp_mocks/freertos/FreeRTOS.h \
 esp_mocks/freertos/task.h esp_mocks/esp_event.h \
 esp_mocks/general_mocks.h esp_mocks/esp_system.h \
 ../mqtt_logs/mqtt_logs.h ../mqtt_logs/mqtt_logs_buffer.h \
 ../date_and_time/date_and_time.h ../MQTT/mqtt.h \
 esp_mocks/freertos/queue.h ../MQTT/reconnect_logic.h \
 ../device_id/device_id.h ../commissioning/commissioning.h
/opt/cpputest/include/CppUTest/MemoryLeakDetectorNewMacros.h:
/opt/cpputest/include/CppUTest/CppUTestConfig.h:
/opt/cpputest/include/CppUTest/CppUTestGeneratedConfig.h:
/opt/cpputest/include/CppUTest/StandardCLibrary.h:
/opt/cpputest/include/CppUTest/MemoryLeakDetectorMallocMacros.h:
esp_mocks/esp_log.h:
esp_mocks/nvs.h:
esp_mocks/esp_err.h:
esp_mocks/esp_timer.h:
esp_mocks/freertos/FreeRTOS.h:
esp_mocks/freertos/task.h:
esp_mocks/esp_event.h:
esp_mocks/general_mocks.h:
esp_mocks/esp_system.h:
../mqtt_logs/mqtt_logs.h:
../mqtt_logs/mqtt_logs_buffer.h:
../date_and_time/date_and_time.h:
../MQTT/mqtt.h:
esp_mocks/freertos/queue.h:
../MQTT/reconnect_logic.h:
../device_id/device_id.h:
../commissioning/commissioning.h:
