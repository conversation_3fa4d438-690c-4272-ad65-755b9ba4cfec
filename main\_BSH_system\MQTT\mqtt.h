/**
 * Module description:
 * 
 * Creates its own task.
 * Implements device-INDEPENDENT funcions of sending to broker, receiving from broker
 * and connecting to broker.
 * 
 * At initialization shall be provided with
 *  - broker address
 *  - user id, passw
 *  - broker in / out topics
 *  - queue of strings to read messages to send
 *  - function to call to treat incoming data
 *  - event loop handle to post events
 */
#pragma once



#include <stdint.h>
#include <stdbool.h>

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "esp_event.h"
#include "reconnect_logic.h"



namespace bsh_sys::mqtt {



#define MQTT_MAX_OUT_DATA_LEN 350
#define MQTT_OUT_QUEUE_OBJ_SIZE (MQTT_MAX_OUT_DATA_LEN + 2 + 1)  // 1 byte with qos and 2 bytes with data length
#define MQTT_OUT_QUEUE_SIZE 5

typedef struct {
    uint16_t data_len;
    uint8_t qos_level;
    char out_data[MQTT_OUT_QUEUE_OBJ_SIZE];
} mqtt_out_obj_t;



ESP_EVENT_DECLARE_BASE(MQTT_EVENTS);

typedef enum {
    MQTT_BROKER_CONNECTED,
    MQTT_BROKER_DISCONNECTED,
    MQTT_BROKER_SUBSCRIBED,
    MQTT_BROKER_TRANPORT_ERR,
    MQTT_BROKER_CONNECTION_REFUSED,
    MQTT_BROKER_ERR,

    MQTT_BROKER_MSG_PUBLISHED,
    MQTT_BROKER_QTY,
} mqtt_events_t;



typedef struct {
    void (* incoming_packet_treat)(uint8_t *data, uint16_t  data_len);  // data len is ptr coz function cuts off signature
    esp_event_loop_handle_t loop_handle;
} mqtt_settings_t;

typedef struct {
    char *broker_addr;
    char *user_id;
    char *device_guid;
    char* dev_id_as_string;
    char *user_passw;
    bool auto_reconnect;
    char *write_topic;
    char *read_topic;
} connection_settings_t;



/**
 *      @brief Starts mqtt service. does NOT connect to broker
 *      @param queue_stts struct with parameters. 
 */
esp_err_t start(mqtt_settings_t *mqtt_sttgs);



/**
 * REturns sending queue, of objects mqtt_out_obj_t
 */
QueueHandle_t get_sending_queue();



/**
 * Stops and delets mqtt task
 */
void stop();



/**
 * Stop reconnect attempts   
 * In case of wrong creds etc
 */
void stop_reconnect();



/**
 *      @brief Connects to broker.
 *              Supposed to be used when need to change creds and reconnect.
 *      @param conn_settings struct with connection settings. 
 *          !dont free the data after calling this funct as its not copied
 */
esp_err_t connect(connection_settings_t *conn_settings);



/**
 *  disconnects from broker
 */
void disconnect_from_broker();



/**
 *  returns connection status
 */
bool is_broker_connected();



/*******************************
 * 
 *     For debug
 *
 *******************************/

/**
 * Posts string to current mqtt topic - mostly FOR DEBUG. shortcuts mqtt queue
 * If can't send (not connected , etc) returns -1, else returns message id
 * for QoS 0 message_id will always be zero
 * BLOCKING function. could be up to 10 sec if no network response
 */
int mqtt_post_string (char* string_to_post, uint16_t len, int QOS);

int mqtt_post_string_to_topic(const char *topic, const char *string, int QOS);



/** 
 * Fakes incoming data.
 * Doesn't check data by any means.
 */
void fake_incoming_pkt(uint8_t *data, uint16_t data_len);



}  // namespace
