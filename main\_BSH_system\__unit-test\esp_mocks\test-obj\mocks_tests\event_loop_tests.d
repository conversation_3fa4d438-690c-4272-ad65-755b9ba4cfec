test-obj/mocks_tests/event_loop_tests.o: mocks_tests/event_loop_tests.cpp \
 /opt/cpputest/include/CppUTest/MemoryLeakDetectorNewMacros.h \
 /opt/cpputest/include/CppUTest/CppUTestConfig.h \
 /opt/cpputest/include/CppUTest/CppUTestGeneratedConfig.h \
 /opt/cpputest/include/CppUTest/StandardCLibrary.h \
 /opt/cpputest/include/CppUTest/MemoryLeakDetectorMallocMacros.h \
 /opt/cpputest/include/CppUTest/TestHarness.h \
 /opt/cpputest/include/CppUTest/Utest.h \
 /opt/cpputest/include/CppUTest/SimpleString.h \
 /opt/cpputest/include/CppUTest/UtestMacros.h \
 /opt/cpputest/include/CppUTest/TestResult.h \
 /opt/cpputest/include/CppUTest/TestFailure.h \
 /opt/cpputest/include/CppUTest/TestPlugin.h \
 /opt/cpputest/include/CppUTest/MemoryLeakWarningPlugin.h \
 /opt/cpputest/include/CppUTest/MemoryLeakDetectorNewMacros.h esp_event.h \
 esp_err.h general_mocks.h
/opt/cpputest/include/CppUTest/MemoryLeakDetectorNewMacros.h:
/opt/cpputest/include/CppUTest/CppUTestConfig.h:
/opt/cpputest/include/CppUTest/CppUTestGeneratedConfig.h:
/opt/cpputest/include/CppUTest/StandardCLibrary.h:
/opt/cpputest/include/CppUTest/MemoryLeakDetectorMallocMacros.h:
/opt/cpputest/include/CppUTest/TestHarness.h:
/opt/cpputest/include/CppUTest/Utest.h:
/opt/cpputest/include/CppUTest/SimpleString.h:
/opt/cpputest/include/CppUTest/UtestMacros.h:
/opt/cpputest/include/CppUTest/TestResult.h:
/opt/cpputest/include/CppUTest/TestFailure.h:
/opt/cpputest/include/CppUTest/TestPlugin.h:
/opt/cpputest/include/CppUTest/MemoryLeakWarningPlugin.h:
/opt/cpputest/include/CppUTest/MemoryLeakDetectorNewMacros.h:
esp_event.h:
esp_err.h:
general_mocks.h:
