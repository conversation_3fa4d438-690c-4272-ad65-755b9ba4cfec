#include <string.h>
#include "queue.h"


#define MOCKED_QUEUES_QTY 20



static QueueDefinition_t queues_array[MOCKED_QUEUES_QTY];
static uint8_t next_free_queue_slot = 0;  // queues deletion from array is not supported as not needed



QueueHandle_t xQueueCreate( int uxQueueLength, int uxItemSize )
{
    if(next_free_queue_slot > MOCKED_QUEUES_QTY)
    {
        printf("\n \n \n ERRORS - queue mocks list is full \n \n \n");
        return NULL;
    }

    QueueDefinition_t *q = &queues_array[next_free_queue_slot];
    next_free_queue_slot ++;
    q->item_size = uxItemSize;
    q->queue_length = uxQueueLength;
    q->uxMessagesWaiting = 0;
    q->pcHead = (uint8_t *) malloc(uxQueueLength*uxItemSize);
    q->pcWriteTo = q->pcHead;

    // printf("created queue N %i", next_free_queue_slot-1);
    return q;
}



int xQueueSend(QueueHandle_t xQueue, const void * const pvItemToQueue, uint32_t xTicksToWait )
{
    if (xQueue->uxMessagesWaiting == xQueue->queue_length)
    {
        printf ("\n error,  queue is full \n");
        return errQUEUE_FULL;
    }

    memcpy(xQueue->pcWriteTo, pvItemToQueue, xQueue->item_size);
    xQueue->uxMessagesWaiting ++;
    xQueue->pcWriteTo = (xQueue->pcWriteTo == xQueue->pcHead + (xQueue->queue_length - 1)*xQueue->item_size) ?  
                            xQueue->pcHead : xQueue->pcWriteTo + xQueue->item_size;
    return pdTRUE;
}



int xQueueReceive( QueueHandle_t xQueue, void * const pvBuffer, uint32_t xTicksToWait )
{
    if (xQueue->uxMessagesWaiting == 0) return errQUEUE_EMPTY;
    uint8_t *ptr = xQueue->pcWriteTo - xQueue->uxMessagesWaiting*xQueue->item_size;
    if (ptr >= xQueue->pcHead)
    {
        memcpy(pvBuffer, ptr, xQueue->item_size);
    } else {
        ptr = (xQueue->pcHead + xQueue->item_size*xQueue->queue_length) - (xQueue->pcHead - ptr);
        memcpy(pvBuffer, ptr, xQueue->item_size);
    }
    xQueue->uxMessagesWaiting --;
    return pdTRUE;
}



/**
 *    Mock tools
*/
void clear_queues()
{
    for (size_t i = 0; i < next_free_queue_slot; i++)
    {
        free(queues_array[i].pcHead);
    }

    memset(queues_array, 0 , sizeof(queues_array));
}



QueueDefinition_t *get_queue(uint8_t N)
{
    if (N > MOCKED_QUEUES_QTY)
    {
        printf ("wrong queue N");
        return NULL;
    }
    return &queues_array[N];
}
