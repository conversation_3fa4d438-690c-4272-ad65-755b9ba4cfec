# WIFI Submodule CMakeLists.txt
# This file contains the build configuration for the WIFI submodule
# Note: This submodule's sources are included in the parent BSH system component

cmake_minimum_required(VERSION 3.5)

# Define WIFI source files (for documentation and potential future use)
set(WIFI_SRCS
    "wifi.cpp"
)

# Define WIFI include directories (for documentation and potential future use)
set(WIFI_INCLUDE_DIRS
    "."
)

# Note: The actual compilation is handled by the parent _BSH_system/CMakeLists.txt
# This file serves as documentation of the WIFI submodule structure
