 /**
 * <PERSON><PERSON><PERSON> accepts BLE commands. process them and sends replies.
 * 
 * 
 * 
 * Possible commands:
 * 
 * 
 * 
 * All SET commands have to split data in pieces. 
 * ***********************************
 * SET_WIFI_SSID       0x10   start passing data
 *                     0x11   data
 *                     0x12   finish passing data
 *                     0x13   cancel
 * ***********************************
 *      start setting packet:
 *      [0x10]                                 0x10 = start 
 *      [ssid length  2 bytes]                 ssid total length.  little endian  3 = 0x03 0x00
 *      [ssid pieces qty]                      total quantity of pieces
 *      [ssid piece size]                      last piece may not be this size
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x10 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok   or 0x01 = error]  status   
 *          
 *      
 *      sending data
 *      [0x11]                                 0x11 = sending
 *      [ssid piece N]                         current piece N. starting from 1
 *      [ssid piece length]                    length of current piece
 *      [ssid ... bytes]                       data 
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x11 ]                        repeating command 
 *             [ piece N ]                     repeating piece N
 *             [ 0x00 = ok   or 0x01 = error]  status           
 * 
 *    
 *      finished setting packet: 
 *      [0x12]                                 0x12 = finish
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x12 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok or 0x?? = error]    status     (0x11 == ssid accepted)
 * 
 *
 *      cancel setting packet:
 *      [0x13]                                  0x13 = cancel
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x13 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok or 0x01 = error]    status 
 *              
 * 
 *        
 * *************************************
 * SET_WIFI_PSWD       0x20   start
 *                     0x21   data
 *                     0x22   finish
 *                     0x23   cancel
 * *************************************
 *      start setting packet:
 *      [0x20]                                 0x20 = start
 *      [pswd length  2 bytes]                 big endian  3 = 0x03 0x00
 *      [pswd piece qty]                       total quantity of pieces
 *      [pswd piece size]                      last piece may not be this size
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x20 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok   or 0x?? = error]  status   
 * 
 * 
 *      sending data
 *      [0x21]                                 0x21 = sending
 *      [pswd piece N]                         current piece N. starting from 1
 *      [pswd piece length]                    length of current piece
 *      [pswd ... bytes]                       data 
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x21 ]                        repeating command 
 *             [ piece N ]                     repeating piece N
 *             [ 0x00 = ok   or 0x?? = error]  status           
 * 
 * 
 *      finished setting packet: 
 *      [0x22]                                 0x22 = finish
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x22 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok or 0x?? = error]    status
 *  
 * 
 *      cancel setting packet:
 *      [0x23]                                  0x23 = cancel
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x23 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok or 0x?? = error]    status 
 * 
 * 
 * 
 **********************************
 * CONNECT             0x30
 ********************************* 
 *      [CONNECT  byte]             command to connect: to wifi, then to balancer, then to broker
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x30 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ status ]                      status 
 *  
 *      regarding status:
 *      connect status/errors are defined 
 *              in mqtt.h (connect_errors_t)
 *              in wifi.h (wifi_conn_result_t)
 *              in http_request_to_balancer.h (balancer_events_t)
 * 
 * 
 **********************************
 * Step-by-step commands 31,32,33 can be used instead of 30 
 * CONNECT TO WIFI      0x31  
 ********************************** 
 *      [0x31]                                 single byte
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x31 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ status ]                      status 
 *  
 **********************************
 * CONNECT TO BALANCER  0x32
 ********************************* 
 *      [0x32]                                  single byte
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x32 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ status ]                      status 
 * 
 **********************************
 * CONNECT TO BROKER  0x33
 ********************************* 
 *      [0x33]                                  single byte
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x33 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ status ]                      status 
 * 
 *********************************
 * SET_BALANCER_LINK   0x40     
 *********************************  
 *      start setting packet:
 *      [0x40]                                 0x40 = start
 *      [link length  2 bytes]                 little endian  3 = 0x03 0x00
 *      [link piece qty]                       total quantity of pieces
 *      [link piece size]                      last piece may not be this size
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x40 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok   or 0x?? = error]  status   
 * 
 * 
 *      sending data
 *      [0x41]                                 0x41 = sending
 *      [link piece N]                         current piece N. starting from 1
 *      [link piece length]                    length of current piece
 *      [link ... bytes]                       data 
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x41 ]                        repeating command 
 *             [ piece N ]                     repeating piece N
 *             [ 0x00 = ok   or 0x?? = error]  status           
 * 
 * 
 *      finished setting packet: 
 *      [0x42]                                 0x42 = finish
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x42 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok or 0x?? = error]    status
 *  
 * 
 *      cancel setting packet:
 *      [0x43]                                  0x43 = cancel
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x43 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok or 0x?? = error]    status  
 * 
 * 
 * 
 *********************************
 * SET_DEVICE_MQTT_PASSW   0x50    
 ********************************* 
 *      start setting packet:
 *      [0x50]                                 0x50 = start
 *      [mqtt pswd length  2 bytes]            little endian  3 = 0x03 0x00
 *      [mqtt pswd piece qty]                  total quantity of pieces
 *      [mqtt pswd piece size]                 last piece may not be this size
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x50 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok   or 0x?? = error]  status   
 * 
 * 
 *      sending data
 *      [0x51]                                 0x51 = sending
 *      [mqtt pswd piece N]                    current piece N. starting from 1
 *      [mqtt pswd piece length]               length of current piece
 *      [mqtt pswd ... bytes]                  data 
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x51 ]                        repeating command 
 *             [ piece N ]                     repeating piece N
 *             [ 0x00 = ok   or 0x?? = error]  status           
 * 
 * 
 *      finished setting packet: 
 *      [0x52]                                 0x52 = finish
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x52 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok or 0x?? = error]    status
 *  
 * 
 *      cancel setting packet:
 *      [0x53]                                  0x53 = cancel
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x53 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok or 0x?? = error]    status  
 * 
 * 
 *   
 *********************************
 * SET_USER_ID             0x60   
 *********************************
 *      start setting packet:
 *      [0x60]                                 0x60 = start
 *      [u_id length  2 bytes]                 little endian  3 = 0x03 0x00
 *      [u_id piece qty]                       total quantity of pieces
 *      [u_id piece size]                      last piece may not be this size
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x60 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok   or 0x?? = error]  status   
 * 
 * 
 *      sending data
 *      [0x61]                                 0x61 = sending
 *      [u_id piece N]                         current piece N. starting from 1
 *      [u_id piece length]                    length of current piece
 *      [u_id ... bytes]                       data 
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x61 ]                        repeating command 
 *             [ piece N ]                     repeating piece N
 *             [ 0x00 = ok   or 0x?? = error]  status           
 * 
 * 
 *      finished setting packet: 
 *      [0x62]                                 0x62 = finish
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x62 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok or 0x?? = error]    status
 *  
 * 
 *      cancel setting packet:
 *      [0x63]                                  0x63 = cancel
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x63 ]                        repeating command 
 *             [ 0x00 ]                        spacer
 *             [ 0x00 = ok or 0x?? = error]    status  
 *  
 * 
 * 
 *********************************
 * SEND_MODEL              0x70     ist a request to send device model (Q781)
 *********************************
 *      [0x70]                                  
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x70 ]                        repeating command
 *             [ 1 byte. model length ]        model length
 *             [ model name bytes]             model name  
 * 
 * 
 * 
 *********************************
 * SEND_ID                 0x80     ist a request to send device ID
 *********************************
 *      [0x80]                                  
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x80 ]                        repeating command
 *             [ ID length byte ]              ID length
 *             [ ID  ... bytes ]               ID  
 *      
 * 
 * 
 *********************************
 * SEND_FIRM_VERSION       0x90     is request to send device firmware version
 *********************************
 *      [0x90]                                  
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0x80 ]                        repeating command
 *             [four bytes]                    virmware version
 *                                             for v 3.15 device sends back:    
 *                                             0x00 0x03 0x0f 0x00        
 * 
 *                                             if we ever decide to add more numbers into version:
 *                                             it will be like v 3.15-33
 *                                             [four bytes]: 0x00 0x03 0x0f 0x21
 * 
 * 
 *********************************
 * SEND_CHALLENGE    0xA0   is request to send challenge
 *********************************
 *      [0xA0]                                  
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0xA0 ]                        repeating command
 *             [ 14 bytes of challenge ]       challenge
 * 
 * 
 * 
 *********************************
 * SEND_CHALLENGE_SIGN_SIZE   0xB0   is request to send signature size
 *********************************
 *      [0xB0]                                  
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0xB0 ]                        repeating command
 *             [ 2 bytes ]                     signature size.  int, in little endian  3 = 0x03 0x00
 *                                             right now sign is 256 byte = 0xAA 0xB0 0x00 0x01 packet
 *
 * 
 *********************************
 * SEND_CHALLENGE_SIGN_PIECE_SIZE   0xB1 
 *********************************
 *      [0xB1]                                  
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0xB1 ]                        repeating command
 *             [ 1 byte ]                      piece size
 * 
 * 
 * 
 *********************************
 * SEND_CHALLENGE_SIGN_PIECE  0xB2         
 *********************************
 *      [0xB2]        
 *      [0x??]                                 piece Number    numeration starts from 0                  
 *      --------------------
 *             device reply:
 *             [ 0xAA ]                        device reply byte
 *             [ 0xB2 ]                        repeating command
 *             [PIECE_SIZE (see above) bytes]  piece N 0x??. if last piece - can be less bytes than PIECE_SIZE
 * 
 * 
 * 
 * 
 *   possible status errors in device replies above - see ble_reply_errors_t below   
 * 
 * 
 * 
 * COMMANDS FOR DEBUG / DEVELOPMENT
 * [0xf1, 0xf2, 0xf3]   reset esp
 * [0xf5, data_len, data]  send to uart
 * 
 * 
 */



#pragma once

#include <stdint.h>


namespace bsh_sys::ble_comm {



typedef enum {
    CMD_OK,
    PACKET_ERR,  
    SETTING_ALREADY_STARTED,
    SETTING_NOT_STARTED,
    TOTAL_LENGTH_DOESNT_MATCH_PIECES,
    INTERNAL_ERR,

    WIFI_SSID_ACCEPTED = 0x11,
    WIFI_PSWD_ACCEPTED = 0x21,
    BALANCER_LINK_ACCEPTED = 0x41,
    MQTT_PSWD_ACCEPTED = 0x51,
    MQTT_USER_ID_ACCEPTED = 0x61,
    OTA_CERT_ACCEPTED = 0xC1,
    // 0x64 and below codes belong to mqtt module
} ble_reply_errors_t;

typedef enum {
    CN_RES_MQTT_NO_BALANCER_LINK_SET = 100,      // dont move this under balancer portion below, as this is nailed down by protocol already
    CN_RES_MQTT_NO_USER_NAME_SET,
    CN_RES_MQTT_NO_USER_PSW_SET,
    CN_RES_MQTT_BROKER_CONNECTED,
    CN_RES_MQTT_BROKER_SUBSCRIBED,
    CN_RES_MQTT_BROKER_MSG_PUBLISHED,
    CN_RES_MQTT_BROKER_CANT_REACH_SERVER,
    CN_RES_MQTT_BROKER_CONNECTION_REFUSED,
    CN_RES_MQTT_BROKER_NO_LINK_SET,
    CN_RES_MQTT_BROKER_DISCONNECTED,    // new, no such code in q780
    CN_RES_MQTT_BROKER_CONNECTING,    // new, no such code in q780
    CN_RES_MQTT_BROKER_NO_WIFI,    // new, no such code in q780


    CN_RES_WIFI_SSID_NOT_SET = 200,  // so doesn't interfere with other mqtt modules errors
    CN_RES_WIFI_PASSW_NOT_SET,
    CN_RES_WIFI_CONNECTED,
    CN_RES_WIFI_CONN_ERR_NETWORK_NOT_FOUND,
    CN_RES_WIFI_CONN_ERR_WRONG_PASSW,
    CN_RES_WIFI_DISCONNECTED,   // new, no such code in q780
    CN_RES_WIFI_CONNECTING,   // new, no such code in q780
    CN_RES_WIFI_CONN_ERR_NO_IP,

    CN_RES_BLR_GOT_LINK = 240,  // so don't enterfere with other mqtt module errors
    CN_RES_BLR_CANT_REACH_BALANCER,
    CN_RES_BLR_CANT_PARCE_LINK,
    CN_RES_BLR_NO_LINK_SET,
    CN_RES_BLR_CONNECTING,
    CN_RES_BLR_NO_WIFI,

} conn_result_t;   // check codes conversion function system_codes_to_ble_codes



/**
 * @brief Processes commands received by BLE control service from cellphone
 * Phone should pass wifi ssid and passw in single packet. (esp will automatically connect upon recieving those)
 * should pass mqtt server link id and password
 * 
 * @param data:  pointer to received packet
 * @param length: length of the data
 */
void got_ble_packet(uint8_t *data, uint16_t length);



/**
 * @brief called by sytem module function, passing connection command result
 * This module communicates them to phone properly.
 * 
 * @param result: connection result
 */
void conn_result(conn_result_t result);




} // bsh_sys::ble_comm namespace
