/**
 * This module provides date and time data.
 * Date is set by calling set_time_from_header(), which should be provided with http date header to parse date from.
 * Source of the header in this project if http request to balancer.
 * When time is set, timer uodates it to keep current time available for system needs.
 */

#pragma once


#include "stdint.h"



#include "esp_err.h"



namespace bsh_sys::date_time {


    #define NO_DATA NULL

    typedef enum {
    SUN, MON, TUE, WEN, THU, FRI, SAT
    } week_day_t;

    typedef enum {
        JAN, FEB, MAR, APR, MAY, JUN, JUL, AUG, SEP, OCT, NOV, DEC
    } month_t;

typedef struct {
    week_day_t week_day;
    uint8_t day_n;
    month_t month;
    uint16_t year;
    uint8_t hour;
    uint8_t min;
    uint8_t sec;
} date_and_time_t; 



/**
 * Sets time&date by Parsing http date header.
 *
 * Expected data/header to be in following format:
 * Fri, 05 May 2023 07:14:26 GMT
 * 
 * @return ESP_OK if ok, 
 *         ESP_ERR_NOT_FOUND if cant parse, 
 *         ESP_ERR_INVALID_ARG if wrong values, like hour = 25
 *         ESP_ERR_NO_MEM if cant create time which ticks to keep time up to date after time is set
 */
esp_err_t set_time_from_header(const char *data);



/**
 * Returns pointer to latest date/time struct.
 * If no data available yet, year == 0
 */
const date_and_time_t *get_date_time();



/**
 * Writes date/time to specified location
*/
void write_date_time(date_and_time_t * where);



/**
 * Substructs specified amount of seconds from date/time
*/
void substract_seconds(date_and_time_t * where, uint64_t seconds);



/**
 * Writes date/time to string. 20 symbols long with '0' at the end
 * Format: yyyy-mm-ddThh:mm:ss
*/
void date_time_to_string(char *where, date_and_time_t *d_t);



} // bsh_sys::date_time