clear
echo -e "\e[1;34m --------- running mock tests --------------- \e[0m"
./main/_BSH_system/__unit-test/docker/run.sh "make -C main/_BSH_system/__unit-test/esp_mocks"

echo ""
echo ""
echo -e "\e[1;34m --------- running bsh tests --------------- \e[0m"
./main/_BSH_system/__unit-test/docker/run.sh "make -C main/_BSH_system/__unit-test"
echo -e "\e[1;34m --------- running bsh tests group 2 ----------- \e[0m"
./main/_BSH_system/__unit-test/docker/run.sh "make -C main/_BSH_system/__unit-test/_unit_tests_group_2"

echo ""
echo ""
echo -e "\e[1;34m --------- running device tests --------------- \e[0m"
./main/__unit-test/docker/run.sh "make -C main/__unit-test"