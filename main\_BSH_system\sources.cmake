# BSH System Sources Configuration
# This file defines all source files, include directories, and embedded files for the BSH system

# BSH system core source files with full paths
set(BSH_ALL_SRCS_WITH_PATH
    # BSH system core sources
    "_BSH_system/system.cpp"
    "_BSH_system/mqtt_helpers.cpp"
    "_BSH_system/mqtt_logs/mqtt_logs.cpp"
    "_BSH_system/mqtt_logs/mqtt_logs_buffer.cpp"
    "_BSH_system/BLE/bluetooth_LE.cpp"
    "_BSH_system/BLE/ble_up_time.cpp"
    "_BSH_system/BLE/first_start.cpp"
    "_BSH_system/ble_comm/ble_comm_logic.cpp"
    "_BSH_system/MQTT/mqtt.cpp"
    "_BSH_system/MQTT/reconnect_logic.cpp"
    "_BSH_system/mqtt_sys_protocol/mqtt_sys_protocol.cpp"
    "_BSH_system/date_and_time/date_and_time.cpp"
    "_BSH_system/request_to_balancer/http_request_to_balancer.cpp"
    "_BSH_system/RSA_encryption/rsa_encryption.cpp"
    "_BSH_system/UART/uart.cpp"
    "_BSH_system/device_id/device_id.cpp"
    "_BSH_system/commissioning/commissioning.cpp"
    "_BSH_system/telemetry_storage/telemetry_storage.cpp"
    
    # BSH system submodule sources
    "_BSH_system/_OTA/bsh_ota.cpp"
    "_BSH_system/_WIFI/wifi.cpp"
)

# BSH system include directories with full paths
set(BSH_ALL_INCLUDE_DIRS_WITH_PATH
    # BSH system include directories
    "./_BSH_system" 
    "./_BSH_system/BLE" 
    "./_BSH_system/ble_comm"
    "./_BSH_system/MQTT" 
    "./_BSH_system/mqtt_sys_protocol" 
    "./_BSH_system/request_to_balancer"
    "./_BSH_system/RSA_encryption"
    "./_BSH_system/UART"
    "./_BSH_system/_WIFI"
    "./_BSH_system/date_and_time"
    "./_BSH_system/device_id"
    "./_BSH_system/commissioning"
    "./_BSH_system/telemetry_storage"
    "./_BSH_system/mqtt_logs"
    "./_BSH_system/_OTA"
)

# BSH system embedded files with full paths
set(BSH_ALL_EMBED_FILES_WITH_PATH
    "_BSH_system/RSA_encryption/esp32_rsa_priv_pair"
    "_BSH_system/RSA_encryption/esp32_rsa_pub_pair"
)
