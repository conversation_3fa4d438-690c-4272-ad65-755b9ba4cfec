//  =============  includes  ==================
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <stdint.h>

#include "esp_log.h"
#include "nvs_flash.h"

#include "ble_internal.h"



namespace bsh_sys::ble_internal
{



//  =============  constants  ==================
static const char *TAG = "- BLE -";
#define FIRST_ON_CONNECTION_TIME 3*24*60*60   //seconds



//  =============  local objects  ==================
static bool first_time_start = true;
static bool loaded = false;



// ================= local functions declarations ========================
static void save_first_time_start (bool first);
static bool load_first_time_start();



// ================= exported functions ========================
bool is_first_time ()
{
    if (!loaded) 
    {
        first_time_start = load_first_time_start();
        loaded = true;
    }

    if (first_time_start)
    {
        if (up_time_get() > FIRST_ON_CONNECTION_TIME)
        {
            first_time_start = false;
            save_first_time_start (false);
        }
    }

    // ESP_LOGI (TAG,"first start: %u", first_time_start);
    return first_time_start;
}



// ================= local functions ========================
static bool load_first_time_start()
{
    nvs_handle _handle;
    esp_err_t err;
    uint8_t first;    
    
    ESP_ERROR_CHECK(nvs_open("ble_cfg", NVS_READWRITE, &_handle));
    
    err = nvs_get_u8(_handle, "first", &first);
    if (err != ESP_OK) 
    {
        nvs_set_u8(_handle, "first", 1);
        nvs_commit(_handle);
        nvs_close(_handle);
        return true;

    } else {
        nvs_close(_handle);
        ESP_LOGI (TAG, "loaded first time: %u",first);
        return first;
    }
}



static void save_first_time_start (bool first)
{
    nvs_handle _handle;

    ESP_ERROR_CHECK(nvs_open("ble_cfg", NVS_READWRITE, &_handle));

    nvs_set_u8(_handle, "first", first);
    nvs_commit(_handle);
    nvs_close(_handle);

    ESP_LOGI (TAG,"first time saved: %u", first);
}


} // bsh_sys::ble_internal namespace
 
