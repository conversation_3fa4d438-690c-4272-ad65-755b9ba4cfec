#pragma once


#define DEVICE_MANUFACTURER_CODE 1

#define CONNECTION_TIME 15*60           // seconds

// chars are same for all Bork devices
#define GATTS_SERVICE_UUID_CONTROL   0x00F3
#define GATTS_CHAR_DEVICE_TYPE  0xF301
#define GATTS_CHAR_FIRM_VER     0xF302
#define GATTS_CHAR_RX           0xF303
#define GATTS_CHAR_TX           0xF304
#define GATTS_CHAR_STATUS       0xF305
#define GATTS_CHAR_TELEM        0xF306



// для q780 - first try of smart home code. hence not same as above
//   характеристики:  
// control service:       000000F2-0000-1000-8000-00805f9b34fb
// RX(прием)              0000F201-0000-1000-8000-00805f9b34fb
// TX(передача)           0000F202-0000-1000-8000-00805f9b34fb
