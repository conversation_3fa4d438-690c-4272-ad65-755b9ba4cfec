#include <stdbool.h>

#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
// #include "https_ota/bsh_https_ota.h"
#include "esp_ota_ops.h"
#include "esp_http_client.h"
#include "esp_https_ota.h"
#include "nvs.h"

#include "bsh_ota.h"


namespace bsh_sys::ota {



#define CONNECTION_TIMEOUT 20000  // ms
#define HTTP_REQUEST_SIZE 16384
#define NVS_CERT_NAMESPACE "cert"
#define NVS_CERT_KEY "cert_data"



/* ======= local object declarations =========*/
static int ota_progr_percent = 0;
static const char *TAG = "- OTA -";
static esp_http_client_config_t config = {};
static bool ota_is_in_progress = false;
static esp_event_loop_handle_t loop_hdl;
static int total_image_size = 1;
static bool dont_verify_cert = false;
static char *cert_buffer = NULL;



ESP_EVENT_DEFINE_BASE(OTA_EVENTS);

/* ======= local function declarations ========= */
static void ota_progr_percentage(int progr);
static void ota_task(void *);
static esp_err_t _http_client_init_cb(esp_http_client_handle_t http_client);
static esp_err_t validate_image_header(esp_app_desc_t *new_app_info);
static int load_certificate(char **buffer);



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
int ota_in_progress()
{
    if (ota_is_in_progress) return ota_progr_percent;
    return 0;
}



void ota_start(const char * firmware_link, 
                    esp_event_loop_handle_t loop_handle,
                    char *user_name, 
                    char *password)
{
    if (ota_is_in_progress) 
    {
        ESP_LOGW (TAG,"OTA is already in progress");
        return;
    }

    if (loop_handle == NULL)
    {
        ESP_LOGE (TAG,"NULL handle provided");
        return;
    }

    if (firmware_link == NULL)
    {
        ESP_LOGE (TAG,"NULL link provided");
        return;

    }

    loop_hdl = loop_handle;
    
    if (dont_verify_cert) 
    {
        config.cert_pem = NULL;
        // чтобы работало без верификации сервера надо установить  CONFIG_OTA_ALLOW_HTTP = y
        // если сертификат != NULL , верификация будет, CONFIG_OTA_ALLOW_HTTP не имеет значения
    } else {
        int err = load_certificate(&cert_buffer);
        if (err != ESP_OK)
        {
            ESP_LOGE(TAG,"failed to load certificate. OTA canceled");
            return;
        }
        config.cert_pem = cert_buffer;
    }
    config.url = firmware_link;
    config.transport_type = HTTP_TRANSPORT_OVER_SSL;
    config.timeout_ms = CONNECTION_TIMEOUT;
    config.keep_alive_enable = true;
    config.skip_cert_common_name_check = true;
    config.buffer_size = OTA_BUFFER_SIZE;   // ?
    config.auth_type = HTTP_AUTH_TYPE_BASIC;
    config.username = user_name;
    config.password = password;

    // start OTA
    printf ("/n");
    ESP_LOGI (TAG, "starting OTA. free heap: %u \n",xPortGetFreeHeapSize());
    ota_is_in_progress = true;

    ESP_LOGW (TAG,"event posted - ota started");
    esp_event_post_to(loop_hdl, OTA_EVENTS, OTA_EVT_STARTED, NULL, 0, 0);

    xTaskCreate(ota_task, "ota_task", 4096, NULL, tskIDLE_PRIORITY + 1, NULL);
}



void set_dont_verify_cert_flag(bool dont_verify)
{
    dont_verify_cert = dont_verify;
    ESP_LOGI (TAG,"server verification set to %i", dont_verify);
}



int save_new_sertificate(char *cert)
{
    esp_err_t err;
    nvs_handle_t nvs_handle;

    err = nvs_open(NVS_CERT_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Err opening NVS: %s", esp_err_to_name(err));
        return err;
    }

    err = nvs_set_blob(nvs_handle, NVS_CERT_KEY, cert, strlen(cert));
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Err writing blob: %s", esp_err_to_name(err));
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Err saving changes: %s", esp_err_to_name(err));
    } else {
        ESP_LOGI(TAG, "New cert successfully saved");
    }

    nvs_close(nvs_handle);
    
    return ESP_OK;
}



/*===============================================*\
 * Local function definitions
\*===============================================*/
static void ota_progr_percentage(int progr)
{
    ota_progr_percent = progr;
    esp_event_post_to(loop_hdl, OTA_EVENTS, OTA_EVT_PERCENT, &ota_progr_percent, sizeof(ota_progr_percent), 0);
}



static void ota_task(void *) 
{
    static int ota_progress = 0;
    esp_err_t ota_finish_err = ESP_OK;
    
    esp_https_ota_config_t ota_config = {
        .http_config = &config,
        .http_client_init_cb = _http_client_init_cb, // Register a callback to be invoked after esp_http_client is initialized
        .partial_http_download = true,
        .max_http_request_size = HTTP_REQUEST_SIZE,
    };
    
    esp_https_ota_handle_t https_ota_handle = NULL;
    esp_err_t err = esp_https_ota_begin(&ota_config, &https_ota_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "ESP HTTPS OTA Begin failed");
        ota_is_in_progress = false;
        vTaskDelete(NULL);
    }
    total_image_size = esp_https_ota_get_image_size(https_ota_handle);
    
    esp_app_desc_t app_desc;
    err = esp_https_ota_get_img_desc(https_ota_handle, &app_desc);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "esp_https_ota_read_img_desc failed");
        goto ota_end;
    }
    err = validate_image_header(&app_desc);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "image header verification failed");
        goto ota_end;
    }

    while (1) {
        err = esp_https_ota_perform(https_ota_handle);
        if (err != ESP_ERR_HTTPS_OTA_IN_PROGRESS) {
            break;
        }
        // esp_https_ota_perform returns after every read operation which gives user the ability to
        // monitor the status of OTA upgrade by calling esp_https_ota_get_image_len_read, which gives length of image
        // data read so far.
        // ESP_LOGD(TAG, "Image bytes read: %d", esp_https_ota_get_image_len_read(https_ota_handle));
        int progress = esp_https_ota_get_image_len_read(https_ota_handle) * 100 / total_image_size;
        if (ota_progress != progress) 
        {
            ota_progress = progress;
            ota_progr_percentage(ota_progress);
        }
    }

    if (esp_https_ota_is_complete_data_received(https_ota_handle) != true) {
        // the OTA image was not completely received and user can customise the response to this situation.
        ESP_LOGE(TAG, "Complete data was not received.");
    } else {
        ota_finish_err = esp_https_ota_finish(https_ota_handle);
        if ((err == ESP_OK) && (ota_finish_err == ESP_OK)) {
            ESP_LOGI(TAG, "ESP_HTTPS_OTA upgrade successful. Rebooting ...");
            vTaskDelay(1000 / portTICK_PERIOD_MS);
            esp_restart();
        } else {
            switch (ota_finish_err)
            {
                case ESP_ERR_OTA_VALIDATE_FAILED: 
                    esp_event_post_to(loop_hdl, OTA_EVENTS, OTA_EVT_ERR_WRONG_FIRMWARE, NULL, 0, 0); 
                    ESP_LOGE(TAG, "Image validation failed, image is corrupted");
                    break; 
                    case ESP_ERR_NOT_FINISHED: 
                    esp_event_post_to(loop_hdl, OTA_EVENTS, OTA_EVT_ERR_DOWNLOAD_CRASHED, NULL, 0, 0); 
                    ESP_LOGE(TAG, "ota not finished err");
                    break;
                case ESP_ERR_NOT_FOUND: 
                    esp_event_post_to(loop_hdl, OTA_EVENTS, OTA_EVT_ERR_WRONG_LINK, NULL, 0, 0); 
                    ESP_LOGE(TAG, "ota not found err. wrong link");
                    break;
                    
                default: 
                    esp_event_post_to(loop_hdl, OTA_EVENTS, ota_finish_err - ESP_ERR_HTTP_BASE, NULL, 0, 0); 
                    ESP_LOGE(TAG, "ESP_HTTPS_OTA upgrade failed 0x%x", ota_finish_err);
                    break;
            }
            ota_is_in_progress = false;
            vTaskDelete(NULL);
        }
    }

ota_end:
    esp_https_ota_abort(https_ota_handle);
    ESP_LOGE(TAG, "ESP_HTTPS_OTA upgrade failed");
    ota_is_in_progress = false;
    vTaskDelete(NULL);
}



static esp_err_t _http_client_init_cb(esp_http_client_handle_t http_client)
{
    esp_err_t err = ESP_OK;
    /* Uncomment to add custom headers to HTTP request */
    // err = esp_http_client_set_header(http_client, "Custom-Header", "Value");
    return err;
}



static esp_err_t validate_image_header(esp_app_desc_t *new_app_info)
{
    if (new_app_info == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    const esp_partition_t *running = esp_ota_get_running_partition();
    esp_app_desc_t running_app_info;
    if (esp_ota_get_partition_description(running, &running_app_info) == ESP_OK) {
        ESP_LOGI(TAG, "Running firmware version: %s", running_app_info.version);
    }

// #ifndef CONFIG_EXAMPLE_SKIP_VERSION_CHECK
//     if (memcmp(new_app_info->version, running_app_info.version, sizeof(new_app_info->version)) == 0) {
//         ESP_LOGW(TAG, "Current running version is the same as a new. We will not continue the update.");
//         return ESP_FAIL;
//     }
// #endif

    return ESP_OK;
}



static int load_certificate(char **buffer)
{
    esp_err_t err;
    nvs_handle_t nvs_handle;

    err = nvs_open(NVS_CERT_NAMESPACE, NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) 
    {
        ESP_LOGE(TAG, "Failed to open NVS: %s", esp_err_to_name(err));
        return -1;
    }

    size_t cert_size = 0;
    err = nvs_get_blob(nvs_handle, NVS_CERT_KEY, NULL, &cert_size);
    if (err != ESP_OK || cert_size == 0)
    {
        ESP_LOGE(TAG, "Failed to read cert size. Error: %s", esp_err_to_name(err));
        nvs_close(nvs_handle);
        return -1;
    }

    if (*buffer != NULL) 
    {
        free(*buffer);
        *buffer = NULL;
    }

    *buffer = (char *)calloc(1, cert_size + 1);
    if (*buffer == NULL) 
    {
        ESP_LOGE(TAG, "Failed to allocate memory for certificate");
        nvs_close(nvs_handle);
        return -1;
    }

    err = nvs_get_blob(nvs_handle, NVS_CERT_KEY, *buffer, &cert_size);
    nvs_close(nvs_handle);

    if (err != ESP_OK) 
    {
        ESP_LOGE(TAG, "Failed to read certificate: %s", esp_err_to_name(err));
        free(*buffer);
        *buffer = NULL;
        return -1;
    }

    ESP_LOGI(TAG, "Certificate loaded, size: %u bytes", cert_size);
    ESP_LOG_BUFFER_HEXDUMP(TAG, (void *)(*buffer), cert_size + 1, ESP_LOG_INFO);
    return ESP_OK;
}



}  // end of bsh_sys::ota namespace