#pragma once

#include <stdbool.h>
#include <stdint.h>

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "esp_event.h"



#define BLE_QUEUE_OBJ_SIZE 200
#define BLE_SATUS_CHAR_ARR_SIZE 50



namespace bsh_sys::ble {



ESP_EVENT_DECLARE_BASE (BLE_EVENTS);

typedef enum 
{
    BLE_CONNECTED,
    BLE_PAIRING,
    BLE_PAIRING_STOPPED_BY_TIMEOUT,
    BLE_DISCONNECTED,
} ble_events_t;

typedef struct 
{
    QueueHandle_t *sending_queue;
    QueueHandle_t *receiving_queue;
    esp_event_loop_handle_t loop_handle;
    bool (* incoming_packet_check)(const char *data, const uint16_t data_len);
    const char *device_name;
    uint16_t firware_version_major;
    uint16_t firmware_version_minor;
    uint16_t firmware_version_patch;
    char commit_hash[8];    // 7 symbols + eos
    uint8_t device_code;   // for ble advertizement manufacturer data
    uint8_t device_type[4];   // for device type char
} ble_settings_t;

typedef struct 
{
    uint8_t data[BLE_QUEUE_OBJ_SIZE];
    uint16_t data_len;
} ble_queue_obj_t;



/**
 * Initializes ble stack. 
 * Starts advertizing and and allows connections.
 */
esp_err_t start(ble_settings_t *setts);

void stop();

void allow_connections();
void stop_connections();
void stop_connections_after_while(uint16_t time_seconds);

bool are_connections_allowed ();
bool is_connected();

void disconnect();

// for debug purposes only. normal sending should be done via ble out queue
bool send_via_bt(const uint8_t *data, uint8_t lenth);
bool sent_telemetry_via_bt(const uint8_t *data, uint16_t length);

/**
 * updates status charachteristic value. 
 * supposed to show current device status - for debug purposes
 * length is <= BLE_SATUS_CHAR_ARR_SIZE. excessive - truncated
 */ 
bool update_status_data(const uint8_t *data, uint8_t lenth);


/**
 * @brief Changes device name
 * @param name - what is shown for scanning
 * @param type - what is shown in device name char
 * @param code - shown in manufacturing data 
 */
void change_device_creds (const char *name, const char *type, uint8_t code);



} // bsh_sys::ble namespace
