#include "date_and_time.cpp"
#include "CppUTest/TestHarness.h"

extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}

TEST_GROUP(DateAndTime_Internal)
{
    void setup()
    {
    }

    void teardown()
    {
    }
};

TEST(DateAndTime_Internal, parse_week_day)
{

    using namespace bsh_sys::date_time;

    const char * correct_input_MON = "Mon";
    const char * correct_input_TUE = "Tue";
    const char * correct_input_WEN = "Wed";
    const char * correct_input_THU = "Thu";
    const char * correct_input_FRI = "Fri";
    const char * correct_input_SAT = "Sat";
    const char * correct_input_SUN = "Sun";
    const char * no_day_input = "asdfasdf";
    // const char * null_ptr_input = NULL;

    week_day_t out = TUE;

    bool res = parse_week_day(correct_input_MON, &out);
    CHECK(out == MON);
    CHECK(res);

    out = MON;
    res = parse_week_day(correct_input_TUE, &out);
    CHECK(out == TUE);
    CHECK(res);

    res = parse_week_day(correct_input_WEN, &out);
    CHECK(out == WEN);
    CHECK(res);

    res = parse_week_day(correct_input_THU, &out);
    CHECK(out == THU);
    CHECK(res);

    res = parse_week_day(correct_input_FRI, &out);
    CHECK(out == FRI);
    CHECK(res);

    res = parse_week_day(correct_input_SAT, &out);
    CHECK(out == SAT);
    CHECK(res);

    res = parse_week_day(correct_input_SUN, &out);
    CHECK(out == SUN);
    CHECK(res);



    res = parse_week_day(no_day_input, &out);
    CHECK_FALSE(res);

    // res = parse_week_day(null_ptr_input, &out);
    // CHECK_FALSE(res);
}



TEST(DateAndTime_Internal, parse_month)
{

    using namespace bsh_sys::date_time;

    const char * correct_input_JAN = "Jan";
    const char * correct_input_FEB = "Feb";
    const char * correct_input_MAR = "Mar";
    const char * correct_input_APR = "Apr";
    const char * correct_input_MAY = "May";
    const char * correct_input_JUN = "Jun";
    const char * correct_input_JUL = "Jul";
    const char * correct_input_AUG = "Aug";
    const char * correct_input_SEP = "Sep";
    const char * correct_input_OCT = "Oct";
    const char * correct_input_NOV = "Nov";
    const char * correct_input_DEC = "Dec";

    const char * incorrect_input = "asdf";



    month_t month = FEB;

    bool res = parse_month(correct_input_JAN, &month);
    CHECK(month == JAN);
    CHECK(res);

    month = JAN;
    res = parse_month(correct_input_FEB, &month);
    CHECK(month == FEB);
    CHECK(res);

    res = parse_month(correct_input_MAR, &month);
    CHECK(month == MAR);
    CHECK(res);

    res = parse_month(correct_input_APR, &month);
    CHECK(month == APR);
    CHECK(res);

    res = parse_month(correct_input_MAY, &month);
    CHECK(month == MAY);
    CHECK(res);

    res = parse_month(correct_input_JUN, &month);
    CHECK(month == JUN);
    CHECK(res);

    res = parse_month(correct_input_JUL, &month);
    CHECK(month == JUL);
    CHECK(res);

    res = parse_month(correct_input_AUG, &month);
    CHECK(month == AUG);
    CHECK(res);

    res = parse_month(correct_input_SEP, &month);
    CHECK(month == SEP);
    CHECK(res);

    res = parse_month(correct_input_OCT, &month);
    CHECK(month == OCT);
    CHECK(res);

    res = parse_month(correct_input_NOV, &month);
    CHECK(month == NOV);
    CHECK(res);

    res = parse_month(correct_input_DEC, &month);
    CHECK(month == DEC);
    CHECK(res);



    res = parse_month(incorrect_input, &month);
    CHECK_FALSE(res);
}



TEST(DateAndTime_Internal, parse_int)
{

    using namespace bsh_sys::date_time;

    const char * one_digit_0 = "0";
    const char * one_digit_1 = "1";
    const char * one_digit_2 = "2";
    const char * one_digit_3 = "3";
    const char * one_digit_4 = "4";
    const char * one_digit_5 = "5";
    const char * one_digit_6 = "6";
    const char * one_digit_7 = "7";
    const char * one_digit_8 = "8";
    const char * one_digit_9 = "9";

    const char * two_digits = "52";
    const char * three_digits = "177";
    const char * four_digits = "5829";
    // most long data - year = 4 digits long
    const char * five_digits_within_uint16_t = "65500";
    const char * five_digits_outside_uint16_t = "90000";
    const char * six_digits_always_outside_uint16_t = "100000";

    const char * no_data = "asdfsd";


    // single digit parse tests
    uint16_t out = 1;
    bool res = parse_int(one_digit_0, 1, &out);
    CHECK(out == 0);
    CHECK(res);

    out = 0;
    res = parse_int(one_digit_1, 1, &out);
    CHECK(out == 1);
    CHECK(res);

    res = parse_int(one_digit_2, 1, &out);
    CHECK(out == 2);
    CHECK(res);

    res = parse_int(one_digit_3, 1, &out);
    CHECK(out == 3);
    CHECK(res);

    res = parse_int(one_digit_4, 1, &out);
    CHECK(out == 4);
    CHECK(res);

    res = parse_int(one_digit_5, 1, &out);
    CHECK(out == 5);
    CHECK(res);

    res = parse_int(one_digit_6, 1, &out);
    CHECK(out == 6);
    CHECK(res);

    res = parse_int(one_digit_7, 1, &out);
    CHECK(out == 7);
    CHECK(res);

    res = parse_int(one_digit_8, 1, &out);
    CHECK(out == 8);
    CHECK(res);

    res = parse_int(one_digit_9, 1, &out);
    CHECK(out == 9);
    CHECK(res);


    // multiple digits parse tests
    res = parse_int(two_digits, 2, &out);
    CHECK(out == 52);
    CHECK(res);

    res = parse_int(three_digits, 3, &out);
    CHECK(out == 177);
    CHECK(res);

    res = parse_int(four_digits, 4, &out);
    CHECK(out == 5829);
    CHECK(res);

    res = parse_int(five_digits_within_uint16_t, 5, &out);
    CHECK(out == 65500);
    CHECK(res);

    // tests to fail
    res = parse_int(five_digits_outside_uint16_t, 5, &out);
    CHECK_FALSE(res);

    res = parse_int(six_digits_always_outside_uint16_t, 6, &out);
    CHECK_FALSE(res);

    res = parse_int(no_data, 3, &out);
    CHECK_FALSE(res);
}

