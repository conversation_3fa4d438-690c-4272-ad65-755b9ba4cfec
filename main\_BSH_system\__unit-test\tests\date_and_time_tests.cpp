#include <string.h>
#include "date_and_time.h"
#include "CppUTest/TestHarness.h"
#include "esp_timer.h"

extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}

TEST_GROUP(DateAndTime)
{
    void setup()
    {
    }

    void teardown()
    {
        clear_esp_timer_mock();
    }
};

TEST(DateAndTime, set_time_from_header_passes)
{
    using namespace bsh_sys::date_time;

    const char * input = "Fri, 05 May 2023 07:14:26 GMT";

    esp_err_t res = set_time_from_header(input);
    const date_and_time_t *out = get_date_time();

    CHECK(out->week_day == FRI);
    CHECK(out->day_n == 5);
    CHECK(out->month == MAY);
    CHECK(out->year == 2023);
    CHECK(out->hour == 7);
    CHECK(out->min == 14);
    CHECK(out->sec == 26);
    CHECK(res == ESP_OK);
}


TEST(DateAndTime, set_time_from_header_fails)
{
    using namespace bsh_sys::date_time;

    const char * input_wrong_symbol = "Fri,, 05 May 2023 07:14:26 GMT";
    esp_err_t res = set_time_from_header(input_wrong_symbol);
    CHECK(res == ESP_ERR_NOT_FOUND);



    const char * input_err_in_day = "Fru, 05 May 2023 07:14:26 GMT";
    res = set_time_from_header(input_err_in_day);
    CHECK(res == ESP_ERR_NOT_FOUND);



    const char * input_25_hour = "Fri, 05 May 2023 25:14:26 GMT";
    res = set_time_from_header(input_25_hour);
    CHECK(res == ESP_ERR_INVALID_ARG);



    const char * input_65_minute = "Fri, 05 May 2023 25:65:26 GMT";
    res = set_time_from_header(input_65_minute);
    CHECK(res == ESP_ERR_INVALID_ARG);



    const char * input_67_second = "Fri, 05 May 2023 25:25:67 GMT";
    res = set_time_from_header(input_67_second);
    CHECK(res == ESP_ERR_INVALID_ARG);
}



TEST(DateAndTime, module_properly_counts_time)
{
    using namespace bsh_sys::date_time;

    const char * input_wrong_symbol = "Fri, 05 May 2023 07:14:26 GMT";
    set_time_from_header(input_wrong_symbol);

    // one second check
    call_timer_cb_if_its_due_time(1000000);  
    const date_and_time_t *out = get_date_time();
    CHECK(out->sec == 27);

    // one minute check
    for (size_t i = 0; i < 60; i++)
    {
        call_timer_cb_if_its_due_time(1000000*i);  
    }
    out = get_date_time();
    CHECK(out->min == 15);

    // one hour check
    for (size_t i = 0; i < 3600; i++)
    {
        call_timer_cb_if_its_due_time(1000000*i);  
    }
    out = get_date_time();
    CHECK(out->hour == 8);

    // one day check
    for (size_t i = 0; i < 24*60*60; i++)
    {
        call_timer_cb_if_its_due_time(1000000*i); 
    }
    out = get_date_time();
    CHECK(out->day_n == 6);
    CHECK(out->week_day == SAT);

    // one month check
    for (size_t i = 0; i < 32*24*60*60; i++)
    {
        call_timer_cb_if_its_due_time(1000000*i);  
    }
    out = get_date_time();
    CHECK(out->month == JUN);

    // one year check
    for (size_t i = 0; i < 365*24*60*60; i++)
    {
        call_timer_cb_if_its_due_time(1000000*i);  
    }
    out = get_date_time();
    CHECK(out->year == 2024);
}



TEST(DateAndTime, substract_seconds)
{
    using namespace bsh_sys::date_time;
    date_and_time_t test_date;

    // Простое вычитание секунд из времени
    test_date.year = 2023;
    test_date.month = OCT;
    test_date.day_n = 12;
    test_date.hour = 12;
    test_date.min = 0;
    test_date.sec = 0;
    substract_seconds(&test_date, 3600); // 1 час
    CHECK(test_date.year == 2023 && test_date.month == OCT && test_date.day_n == 12 && test_date.hour == 11 && test_date.min == 0 && test_date.sec == 0);
    

    // Вычитание, влияющее на все значения
    test_date.year = 2023;
    test_date.month = JAN;
    test_date.day_n = 1;
    test_date.hour = 0;
    test_date.min = 0;
    test_date.sec = 0;
    substract_seconds(&test_date, 1);
    CHECK(test_date.year == 2022 && test_date.month == DEC && test_date.day_n == 31 && test_date.hour == 23 && test_date.min == 59 && test_date.sec == 59);
    
    // Вычитание за пределы минимального значения
    test_date.year = 1970;
    test_date.month = JAN;
    test_date.day_n = 1;
    test_date.hour = 0;
    test_date.min = 0;
    test_date.sec = 0;
    substract_seconds(&test_date, 60); 
    CHECK(test_date.year == 1970 && test_date.month == JAN && test_date.day_n == 1 && test_date.hour == 0 && test_date.min == 0 && test_date.sec == 0);
}



TEST(DateAndTime, to_string)
{
    using namespace bsh_sys::date_time;
    char buffer[20];  // 20 символов (включая null-терминатор) достаточно для "YYYY-MM-DD_HH:MM:SS"
    
    // Проверка стандартной даты и времени
    date_and_time_t test_date1 = {TUE, 15, OCT, 2023, 14, 30, 45};  // 2023-10-15 14:30:45
    date_time_to_string(buffer, &test_date1);
    CHECK(strcmp(buffer, "2023-10-15T14:30:45") == 0);

    // Граничное значение месяца
    date_and_time_t test_date2 = {MON, 1, JAN, 2023, 0, 0, 0};  // 2023-01-01 00:00:00
    date_time_to_string(buffer, &test_date2);
    CHECK(strcmp(buffer, "2023-01-01T00:00:00") == 0);

    // Проверка конца года
    date_and_time_t test_date3 = {WEN, 31, DEC, 1999, 23, 59, 59};  // 1999-12-31 23:59:59
    date_time_to_string(buffer, &test_date3);
    CHECK(strcmp(buffer, "1999-12-31T23:59:59") == 0);
    
    // Проверка нулевого значения времени
    date_and_time_t test_date4 = {SUN, 21, JUN, 2020, 0, 0, 0};  // 2020-06-21 00:00:00
    date_time_to_string(buffer, &test_date4);
    CHECK(strcmp(buffer, "2020-06-21T00:00:00") == 0);
}




/**
 * Writes date/time to string. 20 symbols long with '0' at the end
 * Format: yyyy-mm-dd_hh:mm:ss
*/
// void date_time_to_string(char *where, date_and_time_t *d_t);