#include <string.h>

#include "esp_err.h"

#include "esp_event.h"

#define EVENT_LOOPS_QTY 3                 // max qty of event loops this module can support
#define MAX_EVENT_HANDLERS_PER_LOOP 10    // max event handlers single loop can register



typedef struct 
{
    esp_event_handler_t event_handler;
    esp_event_base_t event_base;
    int32_t event_id;
    void *handler_args;
} handler_t;



static uint8_t loops_registered = 0;
static esp_event_loop_args_t *loops_args[EVENT_LOOPS_QTY] = {};
static handler_t registered_callbacks[EVENT_LOOPS_QTY][MAX_EVENT_HANDLERS_PER_LOOP] = {};



esp_err_t esp_event_loop_create(esp_event_loop_args_t *event_loop_args, 
                                    esp_event_loop_handle_t *event_loop)
{
    if (loops_registered == EVENT_LOOPS_QTY) 
    {
        printf("error: too many loops registered\n");
        return ESP_ERR_NO_MEM;
    }

    loops_registered++;
    loops_args[loops_registered - 1] = event_loop_args;
    *event_loop = &loops_args[loops_registered - 1];

    return ESP_OK;
}



esp_err_t esp_event_handler_register_with(esp_event_loop_handle_t event_loop, esp_event_base_t event_base, int32_t event_id, esp_event_handler_t event_handler, void *event_handler_arg)
{
    uint8_t loop_N = ((uint8_t *)event_loop - (uint8_t *)loops_args)/sizeof(esp_event_loop_args_t);
    
    uint8_t free_handler_position;
    for (size_t i = 0; i < MAX_EVENT_HANDLERS_PER_LOOP; i++)
    {
        if(registered_callbacks[loop_N][i].event_handler == NULL)
        free_handler_position = i;
        break;
    }

    registered_callbacks[loop_N][free_handler_position].event_base = event_base;
    registered_callbacks[loop_N][free_handler_position].event_id = event_id;
    registered_callbacks[loop_N][free_handler_position].event_handler = event_handler;
    registered_callbacks[loop_N][free_handler_position].handler_args = event_handler_arg;

    return ESP_OK;
}



esp_err_t esp_event_post_to(esp_event_loop_handle_t event_loop, esp_event_base_t event_base, int32_t event_id, void *event_data, size_t event_data_size, TickType_t ticks_to_wait)
{
    uint8_t loop_N = ((char *)event_loop - (char *)loops_args)/sizeof(esp_event_loop_args_t);

    for (size_t i = 0; i < MAX_EVENT_HANDLERS_PER_LOOP; i++)
    {
        if(registered_callbacks[loop_N][i].event_base == event_base ||
           registered_callbacks[loop_N][i].event_base == ESP_EVENT_ANY_BASE)
        {
            if(registered_callbacks[loop_N][i].event_id == event_id ||
               registered_callbacks[loop_N][i].event_id == ESP_EVENT_ANY_ID)
            {
                registered_callbacks[loop_N][i].event_handler(
                        registered_callbacks[loop_N][i].handler_args, event_base, event_id, event_data);
            }
        }
    }
    return ESP_OK;
}



/**
 *  Mock tools
*/
UBaseType_t uxTaskPriorityGet(void *xTask)
{
    return 7;
}




void clear_esp_event_mock()
{
    memset(loops_args, 0, sizeof(loops_args));
    memset(registered_callbacks, 0, sizeof(registered_callbacks));
}


void print_event_arrays()
{
    printf("\n loops_args array: \n");
    for (size_t i = 0; i < EVENT_LOOPS_QTY; i++)
    {
        printf("%li:  %p \n",i, loops_args[i]);
    }
}



static void print_handler(handler_t *hdl)
{
    printf ("handler: %p   evt_base: %s   id: %i   args: %p \n", 
            hdl->event_handler, hdl->event_base, hdl->event_id, hdl->handler_args);
}


void print_CB_array()
{
    printf("\n callbacks array: \n");

    for (size_t i = 0; i < EVENT_LOOPS_QTY; i++)
    {
        if (registered_callbacks[i][0].event_handler != NULL)
        {
            for (size_t j = 0; j < MAX_EVENT_HANDLERS_PER_LOOP; j++)
            {
                print_handler(&registered_callbacks[i][j]);
            }
            
        } else {
            printf ("empty line \n");
        }
    }
    

}
