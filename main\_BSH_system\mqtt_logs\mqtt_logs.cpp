#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include <math.h>

#include "esp_log.h"
#include "nvs.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_system.h"

#include "mqtt_logs.h"
#include "mqtt_logs_buffer.h"
#include "date_and_time.h"
#include "mqtt.h"
#include "device_id.h"
#include "commissioning.h"



#define MAX_MSG_SIZE 1500
#define MAX_TAG_SIZE 20
#define LOGS_QOS 0

#define BUFFER_SIZE 30000
#define SHORT_MESSAGE_MAX_SIZE 500



namespace bsh_sys::mqtt_logs {



static const char *TAG = "-mqtt logs-";
static bool init_done = false;
// формат топика для отправки:  /users/{userId}/devices/{deviceId}/logs
static char *logs_topic_name = NULL;


/* ======= local object declarations =========*/
static mqtt_log_level_t log_level = MQTT_LOG_LVL_OFF;
static char *msg_formatting_buffer = NULL;



/* ======= local function declarations ========= */
static void save_log_level(mqtt_log_level_t level);
static void load_log_level(mqtt_log_level_t *level);
static void logs_sending_task(void *);
static void convert_time_stamp(char *write_to, const char *to_parse);
static int  append_json_key_value(char *where, const char *key, const char *value, uint16_t max_len);
static int64_t power (int base, int pw);
static bool check_written_value_ok (int value);
static void prepare_message (const char *source_msg);




/*===============================================*\
 * Exported functions definitions
\*===============================================*/
void init(uint8_t *dev_id, int dev_id_size, char *user_id)
{
    if(init_done) 
    {
        ESP_LOGE(TAG, "multiple inits");
        return;
    }

    load_log_level(&log_level);
    bsh_sys::mqtt_logs::logs_buffer::init_buffer(BUFFER_SIZE);
    init_done = true;

    msg_formatting_buffer = (char *)malloc(MAX_MSG_SIZE + 1);

    esp_err_t ret = xTaskCreate(logs_sending_task, "mqttLogsTask", 1024*12, NULL, 12, NULL);  // need big stack to put together log message
    if (ret != pdPASS) 
    {
        ESP_LOGE(TAG, "Failed to initialize mqtt logs task (%s), restarting device...", esp_err_to_name(ret));
        esp_restart();
    }

    if (dev_id != NULL and user_id != NULL) refresh_topic_name(dev_id, dev_id_size, user_id);

    ESP_LOGI (TAG,"mqtt logs init done.");
}



void deinit()
{
    init_done = false;
    bsh_sys::mqtt_logs::logs_buffer::deinit_buffer();
}



void set_log_level(mqtt_log_level_t level)
{
    log_level = level;
    save_log_level(log_level);
}



mqtt_log_level_t get_logs_level()
{
    return log_level;
}



void post_log_msg(mqtt_log_level_t log_lvl, const char *module_tag, const char *message, uint8_t *hex_dump, uint16_t dump_size, ...)
{   
    using namespace bsh_sys::mqtt_logs::logs_buffer;
    if(!init_done) return;
    if(message == NULL) { ESP_LOGE(TAG, "null pointer msg"); return; }

    if (log_lvl > log_level) return;

    va_list input_args;
    va_start(input_args, message);
    bsh_sys::mqtt_logs::logs_buffer::push_formatted_message(log_lvl, module_tag, message, hex_dump, dump_size, input_args);
    va_end(input_args);
}



char *log_lvl_to_string(mqtt_log_level_t level)
{
    switch (level)
    {
    case MQTT_LOG_LVL_OFF:  return "off"; 
    case MQTT_LOG_LVL_ERR:  return "err";
    case MQTT_LOG_LVL_WARN: return "warn";
    case MQTT_LOG_LVL_INFO: return "info";
    }
    return "off";
}



// формат топика для отправки:  /users/{userId}/devices/{deviceId}/logs
void refresh_topic_name(uint8_t *dev_id, int dev_id_size, char *user_id)
{
    if (dev_id == NULL || user_id == NULL)
    {
        ESP_LOGE(TAG, "null creds provided");
        return;
    }

    int uid_size = strlen(user_id);

    if(logs_topic_name != NULL) free(logs_topic_name);
    
    const char * start = "/users/";
    const char * middle = "/devices/";
    const char * end = "/logs";

    logs_topic_name = (char*) malloc (strlen(start) + strlen(user_id) + strlen(middle) + dev_id_size*2 + strlen(end) + 2 + 1); 

    // /users/
    memcpy(logs_topic_name, start, strlen(start));

    // /users/user_id
    memcpy (logs_topic_name + strlen(start), user_id, uid_size);

    // /users/user_id/devices
    memcpy (logs_topic_name + strlen(start) + uid_size, middle, strlen(middle));

    // /users/user_id/devices/device_id
    for (size_t i = 0; i < dev_id_size; i++)
    {
        snprintf (logs_topic_name + strlen(start) + strlen(user_id) + strlen(middle) + i*2, 3 , "%02x",*(dev_id+i));
    }

    // /users/user_id/devices/device_id/commands
    memcpy (logs_topic_name + strlen(start) + strlen(user_id) + strlen(middle) + dev_id_size*2, end, strlen(end)+1);
    
    ESP_LOGI(TAG, "commands topic name:  %s", logs_topic_name);
}



/*===============================================*\
 * Local function definitions
\*===============================================*/

/** 
 * Task waits for messages in incoming queue, then sends message to mqtt stack
 * sending api is actually a blocking api
 */
static void logs_sending_task(void *)
{
    char *message = NULL;
    while (1)
    {
        if(logs_topic_name != NULL && bsh_sys::mqtt::is_broker_connected() && bsh_sys::mqtt_logs::logs_buffer::has_message())
        {
            message = bsh_sys::mqtt_logs::logs_buffer::get_message();
            if(message == NULL)
            {
                ESP_LOGE(TAG,"null ptr message");
            } else {
                memset(msg_formatting_buffer, 0, MAX_MSG_SIZE + 1);
                prepare_message(message);
                bsh_sys::mqtt::mqtt_post_string_to_topic(logs_topic_name, msg_formatting_buffer, LOGS_QOS);
                // ESP_LOGI (TAG, "logs out: %s", message);
                ESP_LOGI (TAG, "logs out: %s", msg_formatting_buffer);
            }
        }
        vTaskDelay (pdMS_TO_TICKS(1000));
    }
}



static void prepare_message (const char *source_msg)
{
    using namespace bsh_sys::mqtt_logs::logs_buffer;


    int tag_shift = (int)(strstr(source_msg, "||") - source_msg) + 2; 
    int msg_shift = (int)(strstr(source_msg + tag_shift , "||") - source_msg) + 2;

    // starting '{'
    int written = snprintf(msg_formatting_buffer, MAX_MSG_SIZE, "{\n");

    // version
    written += append_json_key_value(msg_formatting_buffer + written, "version", "1.1", MAX_MSG_SIZE);
    if(!check_written_value_ok(written)){push_message(msg_formatting_buffer); return;}

    // host
    char id[DEVICE_ID_SIZE*2 +1] = {};
    bsh_sys::devid::write_device_id_to_buffer_as_string(id);
    written += append_json_key_value(msg_formatting_buffer + written, "host", id, MAX_MSG_SIZE - written);
    if(!check_written_value_ok(written)){push_message(msg_formatting_buffer); return;}

    // short message
    written += snprintf(msg_formatting_buffer + written, MAX_MSG_SIZE - written, "\"short_message\": \"");
    int total_msg_length = strlen(source_msg) - msg_shift;
    // ESP_LOGW(TAG,"============= taf shift: %i written: %i   msg_shift: %i   total msg size: %i", tag_shift, written, msg_shift, total_msg_length);
    memcpy(msg_formatting_buffer + written, source_msg + msg_shift, total_msg_length >= SHORT_MESSAGE_MAX_SIZE ? SHORT_MESSAGE_MAX_SIZE : total_msg_length);
    written += total_msg_length >= SHORT_MESSAGE_MAX_SIZE ? SHORT_MESSAGE_MAX_SIZE : total_msg_length;
    if(!check_written_value_ok(written)){push_message(msg_formatting_buffer); return;}
    written += snprintf(msg_formatting_buffer + written, MAX_MSG_SIZE - written, "\",\n");
    if(!check_written_value_ok(written)){push_message(msg_formatting_buffer); return;}

    // full message
    written += snprintf(msg_formatting_buffer + written, MAX_MSG_SIZE - written, "\"full_message\": \"");
    memcpy(msg_formatting_buffer + written, source_msg + msg_shift, total_msg_length);
    written += total_msg_length;
    if(!check_written_value_ok(written)){push_message(msg_formatting_buffer); return;}
    written += snprintf(msg_formatting_buffer + written, MAX_MSG_SIZE - written, "\",\n");
    if(!check_written_value_ok(written)){push_message(msg_formatting_buffer); return;}

    // log level
    written += snprintf(msg_formatting_buffer + written, MAX_MSG_SIZE - written, "\"level\": %u,\n", source_msg[0]);
    if(!check_written_value_ok(written)){push_message(msg_formatting_buffer); return;}

    // subsystem
    int tag_size = msg_shift - tag_shift - 2;
    uint8_t tmp[tag_size + 2] = {};   
    memcpy(tmp, source_msg + tag_shift, tag_size);
    written += append_json_key_value(msg_formatting_buffer  + written, "_subsystem", (const char *)tmp, MAX_MSG_SIZE - written);
    if(!check_written_value_ok(written)){push_message(msg_formatting_buffer); return;}

    // date and time
    char time[20] = {};
    convert_time_stamp(time, msg_formatting_buffer + 3);   // first two bytes - total buffer item size. third byte - log level. next - time stamp
    written += append_json_key_value(msg_formatting_buffer  + written, "_datetime", time , MAX_MSG_SIZE - written);
    if(!check_written_value_ok(written)){push_message(msg_formatting_buffer); return;}

    // user id
    written += append_json_key_value(msg_formatting_buffer  + written, "_userId", bsh_sys::commiss::get_mqtt_user_id() == NULL ? "no id yet" : bsh_sys::commiss::get_mqtt_user_id(), MAX_MSG_SIZE - written);
    if(!check_written_value_ok(written)){push_message(msg_formatting_buffer); return;}

    // trailing '}'
    written += snprintf(msg_formatting_buffer + written -2, MAX_MSG_SIZE - written, "\n}");  // -2 to place chars instead of ','
}



static void save_log_level(mqtt_log_level_t level)
{
    nvs_handle _handle;

    ESP_ERROR_CHECK(nvs_open("mqtt_logs", NVS_READWRITE, &_handle));

    nvs_set_u8(_handle, "logs_lvl", (uint8_t)level);
    nvs_commit(_handle);
    nvs_close(_handle);
    ESP_LOGI (TAG, "mqtt logs lvl saved: %u", level);
}



static void load_log_level(mqtt_log_level_t *level)
{
    nvs_handle _handle;
    esp_err_t err;
    uint8_t lvl;    
    
    ESP_ERROR_CHECK(nvs_open("mqtt_logs", NVS_READWRITE, &_handle));
    
    err = nvs_get_u8(_handle, "logs_lvl", &lvl);
    if (err != ESP_OK) 
    {
        ESP_LOGW(TAG, "cant load mqtt logs lvl. set to 0");
        nvs_set_u8(_handle, "logs_lvl", 0);
        nvs_commit(_handle);
        nvs_close(_handle);
    } else {
        nvs_close(_handle);
        *level = (mqtt_log_level_t)lvl;
        ESP_LOGI (TAG, "loaded mqtt logs lvl: %u",*level);
    }
}



static bool check_written_value_ok (int value)
{
    if (value > MAX_MSG_SIZE - 5)
    {
        ESP_LOGW(TAG,"log msg truncated");
        return false;
    }
    return true;
}



/**
 * Appends key-value data in format ' "host": "my-host",\n '
 * returns qty of written symbols, or -1 if message doesnt fir into max_len
*/
static int append_json_key_value(char *where, const char *key, const char *value, uint16_t max_len)
{
    int total_length = snprintf(where, max_len, "\"%s\": \"%s\",\n",key, value);
    if (total_length > max_len) return -1;
    return total_length;
}



static void convert_time_stamp(char *write_to, const char *to_parse)
{
    int64_t system_time = 0;

    // parse system time from message. first 16 symbols are time in string format
    for (size_t i = 16; i > 0 ; i--)
    {
        system_time += (*(to_parse + i - 1) - 48) * power(10, 20 - i);
    }
    system_time = system_time / 1000000; // sys time is in microseconds, but need only seconds

    // put together time stamp in format yyyy-mm-dd_hh:mm:ss
    bsh_sys::date_time::date_and_time_t d_t;
    bsh_sys::date_time::write_date_time(&d_t);
    bsh_sys::date_time::substract_seconds(&d_t, system_time);
    bsh_sys::date_time::date_time_to_string(write_to, &d_t);
}



static int64_t power (int base, int pw)
{
    int64_t res = 1;
    for (size_t i = 0; i < pw; i++)
    {
        res = (int64_t)res*(int64_t)base;
    }
    return res;
}



}   // end of bsh_sys::mqtt_logs namespace


