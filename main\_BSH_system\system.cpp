#include "string.h"

#include "esp_log.h"
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "nvs_flash.h"
#include "esp_timer.h"

#include "system.h"
#include "system_internal.h"
#include "mqtt.h"
#include "mqtt_helpers.h"
#include "bluetooth_LE.h"
#include "rsa_encryption.h"
#include "device_id.h"
#include "commissioning.h"
#include "SYS_LOG_LEVELS.h"
#include "mqtt_logs.h"



#define BLE_STATUS_CHAR_UPDATE_PERIOD 1000000  //us
#define BLE_STATUS_CHAR_SYSTEM_PARAMS_OFFSET 5 // bytes



namespace bsh_sys {



ESP_EVENT_DEFINE_BASE (SMART_HOME_CONN_EVENTS);



/* ======= local typedefs =========*/
typedef struct 
{
    bsh_sys::mqtt::mqtt_settings_t module_settings;
    bsh_sys::mqtt::connection_settings_t conn_sttgs;
} mqtt_sttgs_t;




/* ======= local defines for constants =========*/
#define BLE_ON_TIME 120 //s   time ble is on after power up
#define BLE_RECONNECT_TIME 15*60 //s   time ble is on after ble disconnect
#define DELAY_FOR_PUSH_TLM 500 //ms

static const char *TAG = "-sys-";



/* ======= local object declarations =========*/
static bool ble_init_done = false;
static bool dont_log_wifi_disconnect = false;   // to doesnt log disconnect event in case of reconnect - for mobyle apps
static QueueHandle_t ble_out_queue;
static QueueHandle_t ble_in_queue;
static TaskHandle_t ble_task_handle;
static esp_event_loop_handle_t loop_handle = NULL;
static const char * guid_pref = NULL;
static system_config_t *config = NULL;
static bool auto_connect = false;  // connect through all 3 steps: wifi, balancer, broker, autmatically
static smart_home_conn_events_t network_status = BSH_NO_CONNECTION;
static bool first_start_after_ota = false;
static esp_timer_handle_t every_second_timer = NULL;

static uint8_t status_arr_for_ble[20] = {};

// settings structs, to pass to other functions which don't copy them (i.e. when need to keep pointers alive)
static bsh_sys::uart::uart_settings_t   uart_settings = {};
static bsh_sys::wifi::bsh_wifi_settings_t wifi_settings = {};
static bsh_sys::mqtt::mqtt_settings_t mqtt_module_settings = {};
static          mqtt_sttgs_t mqtt_settings = {};
static bsh_sys::blsr::request_to_balancer_params_t req_blcr_params = {};
static bsh_sys::ble::ble_settings_t ble_sttgs = {};



/* ======= local function declarations ========= */
// ble
static void ble_init(void *);
static void ble_task(void *args);
static bool put_to_ble_queue(const char *data, uint16_t data_len);
static bool ble_incoming_packet_check(const char *data, const uint16_t data_len);

// events
static void ble_event_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data);
static void wifi_event_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data);
static void mqtt_event_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data);
static void blcr_event_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data);
static void mqtt_protocol_event_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data);

// mqtt
static void mqtt_system_commands_task(void *args);
static void send_rssi_cb(int8_t rssi);

// ota
static void save_ota_started(bool is_started);
static bool load_ota_started();
static void ota_event_handler (void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data);

static void CallWithDelay(void (*function)(void*),uint16_t delay);
static void set_log_levels();

static void every_second_timer_cb(void* arg);

static void ble_shut_down_cb(void);


enum status_arr_bytes{
    B_ST_WIFI_CONNECTED,
    B_ST_WIFI_RSSI,
    B_ST_BSH_CONNECTED,
    B_ST_OTA_PROGRESS,

};


/*===============================================*\
 * Exported functions definitions
\*===============================================*/
esp_err_t init (system_config_t *sys_config)
{
    if (sys_config == NULL)
    {
        ESP_LOGE (TAG, "null ptr");
        return ESP_ERR_INVALID_ARG;
    }

    config = sys_config;
    guid_pref = config->guid_prefix;

    first_start_after_ota = load_ota_started();
    if (first_start_after_ota) save_ota_started(false);

    esp_timer_create_args_t timerArgs = {0};
    timerArgs.callback = every_second_timer_cb;
    ESP_ERROR_CHECK(esp_timer_create(&timerArgs, &every_second_timer));
    esp_timer_start_periodic(every_second_timer, 1000000);

    // event loop
    esp_event_loop_args_t loop_with_task_args = {
        .queue_size = 20,
        .task_name = "loop_task", // task will be created
        .task_priority = uxTaskPriorityGet(NULL),
        .task_stack_size = 2048*2,
        .task_core_id = tskNO_AFFINITY,
    };
    ESP_ERROR_CHECK(esp_event_loop_create(&loop_with_task_args, &loop_handle));    
    ESP_ERROR_CHECK(esp_event_handler_register_with(loop_handle, bsh_sys::wifi::WIFI_EVENTS, ESP_EVENT_ANY_ID, wifi_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register_with(loop_handle, bsh_sys::mqtt::MQTT_EVENTS, ESP_EVENT_ANY_ID, mqtt_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register_with(loop_handle, bsh_sys::ble::BLE_EVENTS, ESP_EVENT_ANY_ID, ble_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register_with(loop_handle, bsh_sys::mqtt_protocol::MQTT_SYS_PROTOCOL_EVENTS, ESP_EVENT_ANY_ID, mqtt_protocol_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register_with(loop_handle, bsh_sys::blsr::BLCR_REQ_EVENTS, ESP_EVENT_ANY_ID, blcr_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register_with(bsh_sys::get_event_loop_handle(), bsh_sys::ota::OTA_EVENTS, ESP_EVENT_ANY_ID, ota_event_handler, NULL));

    ESP_LOGI (TAG, "    Initialising hardware...");
    bsh_sys::commiss::init();
    bsh_sys::mqtt_protocol::init(loop_handle);

    // check uart config
    if (config->dont_need_uart == false && ( config->uart_stt->sending_queue == NULL || 
        config->uart_stt->incoming_data_cb == NULL))
    {
        ESP_LOGE (TAG, "wrong uart init args");
        return ESP_ERR_INVALID_ARG;
    }

    if (config->dont_need_uart == false)
    {
        memcpy(&uart_settings, config->uart_stt, sizeof(bsh_sys::uart::uart_settings_t));
    }


    CallWithDelay(ble_init, 2);   // !!! called with delay coz simulteneous start with wifi causes brownout if working from usb !!!
    // Create ble task
    esp_err_t ret = xTaskCreate(ble_task, "ble_queue_task", 4096, NULL, 12, &ble_task_handle);
    if (ret != pdPASS) 
    {
        ESP_LOGE(TAG, "Failed to create ble task (%s), restarting device...", esp_err_to_name(ret));
        esp_restart();
    }
    ESP_LOGI(TAG, "ble task created");

    // Create mqtt system commands task
    ret = xTaskCreate(mqtt_system_commands_task, "mqtt_sys", 4096, NULL, 12, NULL);
    if (ret != pdPASS) 
    {
        ESP_LOGE(TAG, "Failed to create mqtt sys task (%s), restarting device...", esp_err_to_name(ret));
        esp_restart();
    }
    ESP_LOGI(TAG, "mqtt sys task created");

    // init uart
    if (config->dont_need_uart == false)
    {
        bsh_sys::uart::start(&uart_settings);
    }

    // init mqtt
    bsh_sys::telem_storage::init(config->actors_qty + bsh_sys::mqtt_protocol::SYS_ACTORS_QTY);
    bsh_sys::mqtt_helpers::update_firmware_version(config->firware_version_major, config->firmware_version_minor, config->firmware_version_patch);
    bsh_sys::mqtt_helpers::update_ota_progress(0);
    bsh_sys::wifi::send_rssi_regulary(send_rssi_cb, 1);
    mqtt_module_settings.loop_handle = loop_handle;
    mqtt_module_settings.incoming_packet_treat = bsh_sys::mqtt_protocol::incoming_mqtt_data;
    bsh_sys::mqtt::start(&mqtt_module_settings);
    // init mqtt logs
    bsh_sys::mqtt_logs::init(NULL, 0, NULL);
    bsh_sys::mqtt_helpers::update_mqtt_log_level((int)bsh_sys::mqtt_logs::get_logs_level());

    auto_connect = true;  // trying to connect to smart home system at module start
    connect_to_wifi();

    ESP_LOGI (TAG, "    Hardware init done.");
    set_log_levels();
    return ESP_OK;
}



bool is_first_start_after_ota()
{
    return first_start_after_ota;
}



system_config_t *get_system_config()
{
    return config;
}


QueueHandle_t get_mqtt_incoming_queue()
{
    return bsh_sys::mqtt_protocol::get_incoming_queue();
}



bsh_sys::uart::uart_settings_t *get_uart_settings()
{
    return &uart_settings;
}



// *******************************************************
// system internal functions, to be called by ble module
// *******************************************************
void connect_to_smart_home()
{
    static int64_t t = 0;

    if (esp_timer_get_time() - t < 10 * 1000000)
    {
        ESP_LOGE(TAG,"connect command ignored - already in progress");
        ESP_LOGE(TAG,"time: %lli %lli",esp_timer_get_time(), t);
        return ;
    }

    t = esp_timer_get_time();
    auto_connect = true;
    if (bsh_sys::wifi::is_connected()) dont_log_wifi_disconnect = true;
    connect_to_wifi();
}



void no_autoconnect()
{
    auto_connect = false;
}



wifi_conn_result_t connect_to_wifi()
{
    ESP_LOGI (TAG,"connecting to wifi...");

    wifi_settings.loop_handle = loop_handle;

#if USE_DEBUG_WIFI_CREDS   
    wifi_settings.ssid = DEBUG_WIFI_SSID;
    wifi_settings.passw = DEBUG_WIFI_PASSW;
#else
    wifi_settings.ssid = commiss::get_wifi_ssid();
    wifi_settings.passw = commiss::get_wifi_passw();
    if(wifi_settings.ssid == NULL || wifi_settings.passw == NULL)
    {   
        if (config->default_wifi_login != NULL && config->default_wifi_passw != NULL
            && strlen(config->default_wifi_login) < 150 && strlen(config->default_wifi_login) < 150)
        {
            wifi_settings.ssid = (char*)malloc(strlen(config->default_wifi_login)+1);
            wifi_settings.passw = (char*)malloc(strlen(config->default_wifi_passw)+1);
            memcpy(wifi_settings.ssid, config->default_wifi_login, strlen(config->default_wifi_login)+1);
            memcpy(wifi_settings.passw, config->default_wifi_passw, strlen(config->default_wifi_passw)+1);
        }
    }
#endif
    if (wifi_settings.ssid == NULL || strlen(wifi_settings.ssid) == 0 || strlen(wifi_settings.ssid) > 200) 
    {
        ESP_LOGI (TAG,"    no ssid");
        auto_connect = false;  
        bsh_sys::ble_comm::conn_result(bsh_sys::ble_comm::CN_RES_WIFI_SSID_NOT_SET);
        return WIFI_NO_SSID_SET;
    }
    if (wifi_settings.passw == NULL || strlen(wifi_settings.passw) == 0 || strlen(wifi_settings.passw) > 200)
    {
        ESP_LOGI (TAG,"    no passw"); 
        auto_connect = false;
        bsh_sys::ble_comm::conn_result(bsh_sys::ble_comm::CN_RES_WIFI_PASSW_NOT_SET);
        return WIFI_NO_PASSW_SET;
    }

    bsh_sys::blsr::http_request_stop_reconnect();
    bsh_sys::mqtt::stop_reconnect();


    bsh_sys::ble_comm::conn_result(bsh_sys::ble_comm::CN_RES_WIFI_CONNECTING);
    bsh_sys::wifi::start_and_connect(&wifi_settings);
    return WIFI_CONNECTING;
}



blcr_conn_result_t connect_to_balancer()
{
    ESP_LOGI (TAG,"connecting to balancer...");
    if (!bsh_sys::wifi::is_connected()) 
    {
        auto_connect = false;
        ESP_LOGW (TAG,"   no wifi");
        bsh_sys::ble_comm::conn_result(bsh_sys::ble_comm::CN_RES_BLR_NO_WIFI);
        return BLCR_NO_WIFI;
    }

    if (commiss::get_balancerlink() == NULL || strlen(commiss::get_balancerlink()) == 0)
    {
        auto_connect = false;
        ESP_LOGW (TAG, "    blcr link not set");
        bsh_sys::ble_comm::conn_result(bsh_sys::ble_comm::CN_RES_BLR_NO_LINK_SET);
        return BLCR_NO_LINK_SET;
    }

    bsh_sys::ble_comm::conn_result(bsh_sys::ble_comm::CN_RES_BLR_CONNECTING);
    
    req_blcr_params.loop_handle = loop_handle;
    req_blcr_params.balancer_link = commiss::get_balancerlink();
    req_blcr_params.user_name = NULL;
    req_blcr_params.passwd = NULL;
    req_blcr_params.reconnect = true;
    req_blcr_params.output_broker_link = &mqtt_settings.conn_sttgs.broker_addr;
    bsh_sys::blsr::get_mqtt_addr_from_balancer(&req_blcr_params);

    return BLCR_CONNECTING;
}



broker_conn_result_t connect_to_broker()
{
    ESP_LOGI (TAG,"connecting to mqtt...");

    if (!bsh_sys::wifi::is_connected()) 
    {
        ESP_LOGW (TAG,"   no wifi");
        auto_connect = false;
        bsh_sys::ble_comm::conn_result(bsh_sys::ble_comm::CN_RES_BLR_NO_WIFI);
        return MQTT_NO_WIFI;
    }

#if USE_DEBUG_MQTT_CONN            
    mqtt_settings.conn_sttgs.broker_addr = DEBUG_BROKER_ADDR;
#else
    // was setup before this function call
#endif           

#if UDE_DEBUG_MQTT_CREDS
    mqtt_settings.conn_sttgs.user_id = DEBUG_MQTT_UID;
    mqtt_settings.conn_sttgs.user_passw = DEBUG_MQTT_PSW;
    mqtt_settings.conn_sttgs.read_topic = bsh_sys::mqtt_protocol::get_commands_topic_name(mqtt_settings.conn_sttgs.user_id, bsh_sys::devid::get_device_id(), DEVICE_ID_SIZE);
    mqtt_settings.conn_sttgs.write_topic = bsh_sys::mqtt_protocol::get_status_topic_name(mqtt_settings.conn_sttgs.user_id, bsh_sys::devid::get_device_id(), DEVICE_ID_SIZE);
#else
    mqtt_settings.conn_sttgs.user_id = bsh_sys::commiss::get_mqtt_user_id();
    mqtt_settings.conn_sttgs.user_passw = bsh_sys::commiss::get_mqtt_passw();
    mqtt_settings.conn_sttgs.read_topic = bsh_sys::mqtt_protocol::get_commands_topic_name(bsh_sys::commiss::get_mqtt_user_id(), bsh_sys::devid::get_device_id(), DEVICE_ID_SIZE);
    mqtt_settings.conn_sttgs.write_topic = bsh_sys::mqtt_protocol::get_status_topic_name(bsh_sys::commiss::get_mqtt_user_id(), bsh_sys::devid::get_device_id(), DEVICE_ID_SIZE);
#endif
    mqtt_settings.conn_sttgs.dev_id_as_string = bsh_sys::commiss::get_dev_id_as_string();
    mqtt_settings.conn_sttgs.device_guid = bsh_sys::devid::get_guid(guid_pref);
    mqtt_settings.conn_sttgs.auto_reconnect = true;

    if(mqtt_settings.conn_sttgs.broker_addr == NULL) 
    {
        ESP_LOGW (TAG,"   no broker link");
        auto_connect = false;
        return MQTT_NO_BROKER_LINK_SET;
    }

    if (mqtt_settings.conn_sttgs.user_id == NULL)
    {
        ESP_LOGW (TAG,"   no uid");
        auto_connect = false;
        return MQTT_NO_USER_ID_SET;
    }

    if (mqtt_settings.conn_sttgs.user_passw == NULL)
    {
        ESP_LOGW (TAG,"   no pswd");
        auto_connect = false;
        return MQTT_NO_USER_PWSD_SET;
    }

    if ( 
        mqtt_settings.conn_sttgs.device_guid == NULL || 
        mqtt_settings.conn_sttgs.read_topic == NULL || 
        mqtt_settings.conn_sttgs.write_topic == NULL)
    {
        auto_connect = false;
        ESP_LOGE (TAG,"   creds not set %p %p %p", mqtt_settings.conn_sttgs.device_guid, mqtt_settings.conn_sttgs.read_topic, mqtt_settings.conn_sttgs.write_topic);
        return MQTT_NO_USER_PWSD_SET;
    }
    
    bsh_sys::ble_comm::conn_result(bsh_sys::ble_comm::CN_RES_MQTT_BROKER_CONNECTING);
    bsh_sys::mqtt::connect(&mqtt_settings.conn_sttgs);
    return MQTT_CONNECTING;
}



smart_home_conn_events_t get_network_status()
{
    return network_status;
}



void update_ble_status_char(uint8_t *data, uint8_t data_len) 
{
    if (data_len > (BLE_SATUS_CHAR_ARR_SIZE - BLE_STATUS_CHAR_SYSTEM_PARAMS_OFFSET))
    {
        data_len = BLE_SATUS_CHAR_ARR_SIZE - BLE_STATUS_CHAR_SYSTEM_PARAMS_OFFSET;
        ESP_LOGE (TAG,"too long status data");
    } 

    memcpy(status_arr_for_ble + BLE_STATUS_CHAR_SYSTEM_PARAMS_OFFSET, data, data_len);
}



esp_event_loop_handle_t get_event_loop_handle()
{
    return loop_handle;
}



const char *get_ble_device_name()
{
    return  bsh_sys::ble::get_device_name();
}
          


/*===============================================*\
 * Local function definitions
\*===============================================*/

/*******************************************
 *                  WIFI
 *******************************************/

// recieves callback when wifi connection status changes
static void wifi_event_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data)
{
    using namespace bsh_sys::wifi;
    using namespace bsh_sys::ble_comm;

    switch (event_id)
    {
    case WIFI_CONNECTED:
        network_status = BSH_WIFI_CONNECTED;
        esp_event_post_to(loop_handle, SMART_HOME_CONN_EVENTS, BSH_WIFI_CONNECTED, NULL, 0, 0);

        conn_result(CN_RES_WIFI_CONNECTED);
        if (auto_connect) connect_to_balancer();

        status_arr_for_ble[B_ST_WIFI_CONNECTED] = true;
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, "-wifi-", "connected", NULL, 0);
        break;
    
    case WIFI_DISCONNECTED:
        network_status = BSH_NO_CONNECTION;
        esp_event_post_to(loop_handle, SMART_HOME_CONN_EVENTS, BSH_NO_CONNECTION, NULL, 0, 0);

        if (dont_log_wifi_disconnect)
        {
            dont_log_wifi_disconnect = false;
        } else {
            conn_result(CN_RES_WIFI_DISCONNECTED);
        }
        bsh_sys::mqtt::disconnect_from_broker();

        status_arr_for_ble[B_ST_WIFI_CONNECTED] = false;
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, "-wifi-", "disconnected", NULL, 0);
        break;

    case WIFI_CONN_ERR_NETWORK_NOT_FOUND:
        conn_result(CN_RES_WIFI_CONN_ERR_NETWORK_NOT_FOUND);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, "-wifi-", "no network", NULL, 0);
        break;

    case WIFI_CONN_ERR_WRONG_PASSW:
        conn_result(CN_RES_WIFI_CONN_ERR_WRONG_PASSW);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, "-wifi-", "wrong password", NULL, 0);
        break;
    
    case WIFI_CONN_ERR_NO_IP:
        conn_result(CN_RES_WIFI_CONN_ERR_NO_IP);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, "-wifi-", "no ip", NULL, 0);
        break;

    case WIFI_STATUS_QTY:
        break;
    }
}



/*******************************************
 *                  BLE
 *******************************************/
/** 
 * BLE queues are internal to system module,
 * as they are device-independent
 */
static void ble_init(void *)
{
    // creates queues
    ble_out_queue = xQueueCreate(BLE_OUT_QUEUE_SIZE, sizeof(bsh_sys::ble::ble_queue_obj_t));
    ble_in_queue = xQueueCreate(BLE_IN_QUEUE_SIZE, sizeof(bsh_sys::ble::ble_queue_obj_t));
    if (ble_out_queue == NULL || ble_in_queue == NULL)
    {
        ESP_LOGE(TAG, "Failed to init ble queue, restarting device...");
        esp_restart();
    }

    // starts ble module
    ble_sttgs.sending_queue = &ble_out_queue;
    ble_sttgs.receiving_queue = &ble_in_queue;
    ble_sttgs.incoming_packet_check = ble_incoming_packet_check;
    ble_sttgs.loop_handle = loop_handle;
    ble_sttgs.device_name = config->ble_device_name;
    ble_sttgs.firware_version_major = config->firware_version_major;
    ble_sttgs.firmware_version_minor = config->firmware_version_minor;
    ble_sttgs.firmware_version_patch = config->firmware_version_patch;
    memcpy(ble_sttgs.commit_hash, config->commit_hash, 7);
    ble_sttgs.device_code = config->ble_device_id_code;
    memcpy(ble_sttgs.device_type, config->ble_device_type, 4);
    ble_init_done = true;
    ESP_LOGI (TAG,"Starting BLE..");
    bsh_sys::ble::start(&ble_sttgs);
    bsh_sys::ble::stop_connections_after_while_and_shut_down_ble(BLE_ON_TIME, ble_shut_down_cb);
}



static void ble_task(void *args)
{
    while (1)
    {
        if (ble_init_done)
        {
            bsh_sys::ble::ble_queue_obj_t incoming_packet;
            xQueueReceive( ble_in_queue, &incoming_packet, portMAX_DELAY);
            bsh_sys::ble_comm::got_ble_packet(incoming_packet.data, incoming_packet.data_len);
        }
        vTaskDelay (pdMS_TO_TICKS(10));
    }
}



static void every_second_timer_cb(void* arg)
{
    static int64_t time = 0;

    // regular updates of BLE status char
    if(bsh_sys::ble::is_connected() && time < (esp_timer_get_time() + BLE_STATUS_CHAR_UPDATE_PERIOD))
    {
        time += BLE_STATUS_CHAR_UPDATE_PERIOD;
        bsh_sys::ble::update_status_data(status_arr_for_ble, sizeof(status_arr_for_ble));
    } 
}



// recieves callback when ble connection status changes
static void ble_event_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data)
{

    using namespace bsh_sys::ble;
    switch (event_id)
    {
    case BLE_PAIRING:
        network_status = BSH_PAIRING;        
        break;
    case BLE_DISCONNECTED:
        // // allow connections for 15 minutes
        bsh_sys::ble::allow_connections();
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, "-ble-", "disconnected", NULL, 0);
        bsh_sys::ble::stop_connections_after_while_and_shut_down_ble(BLE_RECONNECT_TIME, ble_shut_down_cb);
        // no brake here - deliberetely
    case BLE_CONNECTED:
        if(event_id == BLE_CONNECTED)bsh_sys::mqtt::disconnect_from_broker();;
        if(event_id == BLE_CONNECTED)bsh_sys::wifi::stop_reconnecting();
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, "-ble-", "connected", NULL, 0);
    case BLE_PAIRING_STOPPED_BY_TIMEOUT:
        if (bsh_sys::mqtt::is_broker_connected())
        {
            network_status = BSH_SMART_HOME_CONNECTED;
        } else if (bsh_sys::wifi::is_connected()) {
            network_status = BSH_WIFI_CONNECTED;
        }
        break;
    }
    esp_event_post_to(loop_handle, SMART_HOME_CONN_EVENTS, network_status, NULL, 0, 0);
}



static bool ble_incoming_packet_check(const char *data, const uint16_t data_len)
{
    // TODO
    return true;
}



static bool put_to_ble_queue(const char *data, uint16_t data_len)
{
    // TODO
    return true;
}



/*******************************************
 *                  UART
 *******************************************/



/*******************************************
 *                  MQTT
 *******************************************/
static void mqtt_system_commands_task(void *args)
{
    using namespace bsh_sys::mqtt_protocol;

    while (1)
    {
        mqtt_in_block_t incoming_block = {};

        xQueueReceive( get_incoming_sys_queue(), &incoming_block, portMAX_DELAY);

        switch (get_sys_actor_index(incoming_block.actor))
        {
        case -1:
            ESP_LOGE (TAG, "actor not found");
            break;
        
        case FUP:
            if (incoming_block.action_type == WRITE)
            {
                save_ota_started(true);
                bsh_sys::ota::ota_start(incoming_block.firm_url, 
                                            get_event_loop_handle(), 
                                            bsh_sys::commiss::get_mqtt_user_id(),
                                            bsh_sys::commiss::get_mqtt_passw()); 
            } else {
                bsh_sys::telem_storage::push_single_block(config->actors_qty + bsh_sys::mqtt_protocol::FUP);
            }
            break;

        case WFI:
            bsh_sys::telem_storage::push_single_block(config->actors_qty + bsh_sys::mqtt_protocol::WFI);
            break;

        case FVR:
            bsh_sys::telem_storage::push_single_block(config->actors_qty + FVR);
            break;

        case RTL:
            if (incoming_block.set_value.in_int == 0)
            {
                bsh_sys::telem_storage::stop_regular_telemetry();
            } else {
                bsh_sys::telem_storage::start_regular_telemetry();
            }
            break;

        case RTT:
            bsh_sys::telem_storage::set_regular_telemetry_time(incoming_block.set_value.in_int);
            break;

        case BLE:
            // if (incoming_block.set_value.in_int == 0)
            // {
            //     bsh_sys::ble::stop_connections();
            // } else {
            //     bsh_sys::ble::allow_connections();
            // }
            break;

        case LOG:
            {
                // save log level
                if (incoming_block.set_value.in_int > bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO) 
                {
                    ESP_LOGW(TAG,"wrong mqtt log level");
                    bsh_sys::mqtt_protocol::send_err_message_to_broker(bsh_sys::mqtt_protocol::WRONG_VALUE);
                }
                bsh_sys::mqtt_logs::set_log_level((bsh_sys::mqtt_logs::mqtt_log_level_t)incoming_block.set_value.in_int);

                // push updated block to telemetry storage
                bsh_sys::mqtt_protocol::mqtt_out_block_t block = {
                    .actor = {'L','O','G'},
                    .index = 0,
                    .capability = 0,
                    .action_type = REPLY,
                    .value_type = INT,
                    .value = {.in_int = bsh_sys::mqtt_logs::get_logs_level()},
                };      
                bsh_sys::telem_storage::update_block (get_system_config()->actors_qty + bsh_sys::mqtt_protocol::LOG, &block);

                // push telemetry to server
                uint8_t rpl_code = 1;
                bsh_sys::telem_storage::push_telemetry(ORN_BROKER_CMD, &rpl_code);
                break;
            }
        case OCT:
            ESP_LOG_BUFFER_HEXDUMP( TAG, incoming_block.firm_url , incoming_block.set_value.in_int, ESP_LOG_INFO);
            bsh_sys::ota::save_new_sertificate(incoming_block.firm_url);
            break;
        case OCO:
            bsh_sys::ota::set_dont_verify_cert_flag(incoming_block.set_value.in_int == 0 ? false : true );
            break;
        }
    }
}



static void blcr_event_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data)
{
    ESP_LOGI (TAG, "blcr call result: %lu", event_id);

    using namespace bsh_sys::blsr;
    using namespace bsh_sys::ble_comm;

    switch (event_id)
    {
    case BLR_OK:
        conn_result(CN_RES_BLR_GOT_LINK);
        ESP_LOGI (TAG, "\n\n================================\n===  Got link from balancer  ===\n================================\n\n");
        if (auto_connect) connect_to_broker();
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, "-blcr-", "got link", NULL, 0);
        break;

    case BLR_CANT_REACH_BALANCER:
        conn_result(CN_RES_BLR_CANT_REACH_BALANCER);

        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, "-blcr-", "cant reach server. err: %i", NULL, 0, (int)event_data);
        break;
    
    case BLR_CANT_PARCE_LINK:
        conn_result(CN_RES_BLR_CANT_PARCE_LINK);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, "-blcr-", "cant parse link", NULL, 0);
        break;
    
    case BLR_GOT_TIME:
        bsh_sys::date_time::set_time_from_header((char *)event_data);
        esp_event_post_to(loop_handle, SMART_HOME_CONN_EVENTS, BSH_GOT_TIME, NULL, 0, 0);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, "-blcr-", "got time", NULL, 0);
        
        break;
    case BLR_QTY:
        break;
    }

}



static void mqtt_event_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data)
{
    ESP_LOGI (TAG, "broker callback event: %lu", event_id);
    using namespace bsh_sys::mqtt;
    using namespace bsh_sys::ble_comm;

    switch (event_id)
    {
    case MQTT_BROKER_CONNECTED:
        conn_result(CN_RES_MQTT_BROKER_CONNECTED);

        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, "-mqtt-", "broker connected", NULL, 0);    
        break;
    case MQTT_BROKER_DISCONNECTED:
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, "-mqtt-", "broker disconnected", NULL, 0);    
        // conn_result(CN_RES_MQTT_BROKER_DISCONNECTED);

        // if (bsh_sys::wifi::is_connected()) 
        // {
        //     network_status = BSH_WIFI_CONNECTED;
        //     esp_event_post_to(loop_handle, SMART_HOME_CONN_EVENTS, BSH_WIFI_CONNECTED, NULL, 0, 0);
        // } else {
        //     network_status = BSH_NO_CONNECTION;
        //     esp_event_post_to(loop_handle, SMART_HOME_CONN_EVENTS, BSH_NO_CONNECTION, NULL, 0, 0);
        // }

        // status_arr_for_ble[B_ST_BSH_CONNECTED] = false;

        break;
    case MQTT_BROKER_SUBSCRIBED:
        bsh_sys::mqtt_protocol::send_test_message();
        ESP_LOGI (TAG,"sent first msg");
        conn_result(CN_RES_MQTT_BROKER_SUBSCRIBED);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, "-mqtt-", "broker subscribed", NULL, 0);    
        break;
    case MQTT_BROKER_TRANPORT_ERR:
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, "-mqtt-", "broker transport err", NULL, 0);    
        conn_result(CN_RES_MQTT_BROKER_CANT_REACH_SERVER);
        break;
    case MQTT_BROKER_CONNECTION_REFUSED:
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, "-mqtt-", "broker connection refused", NULL, 0);    
        // bsh_sys::mqtt::stop_reconnect();
        conn_result(CN_RES_MQTT_BROKER_CONNECTION_REFUSED);
        break;
    case MQTT_BROKER_ERR:
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, "-mqtt-", "broker err %i", NULL, 0, (int) event_data);
        break;
    case MQTT_BROKER_MSG_PUBLISHED:
        if(network_status != BSH_SMART_HOME_CONNECTED) 
        {
            esp_event_post_to(loop_handle, SMART_HOME_CONN_EVENTS, BSH_SMART_HOME_CONNECTED, NULL, 0, 0);
            network_status = BSH_SMART_HOME_CONNECTED;
        }
        conn_result(CN_RES_MQTT_BROKER_MSG_PUBLISHED);
        status_arr_for_ble[B_ST_BSH_CONNECTED] = true;
# if USE_DEBUG_MQTT_CONN
        bsh_sys::mqtt_logs::refresh_topic_name(bsh_sys::devid::get_device_id(), DEVICE_ID_SIZE, "no uid yet");
# else 
        bsh_sys::mqtt_logs::refresh_topic_name(bsh_sys::devid::get_device_id(), DEVICE_ID_SIZE, bsh_sys::commiss::get_mqtt_user_id());
# endif
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, "-mqtt-", "broker 1st msg published", NULL, 0);    
        break;
    case MQTT_BROKER_QTY:
        break;
    }
}



static void mqtt_protocol_event_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data)
{
    using namespace bsh_sys::mqtt_protocol;

    switch (event_id)
    {
        case PACKET_IS_OK:
        case MNEMOCODE_NOT_RECOGNIZED:   // not used in sys module. here only for backwards compat
            break;


        case PROTOCOL_VERSION_ERROR:
        case WRONG_ACTOR_TO_WRITE:
        case WRONG_ACTOR_TO_READ:
        case WRONG_READ_WRITE_TYPE:
        case WRONG_INCREMENT_TYPE:
        case WRONG_DATA_TYPE:
        case WRONG_MQTT_STRING_LENGTH:
        case WRONG_VALUE:
        case WRONG_SIGNATURE:

            send_err_message_to_broker(event_id); 
    }
}




// ================  other  ================



/*
 *  Delayed function call
 */
static esp_timer_handle_t delayedCallTimer = NULL;
static void CallWithDelayCancel()
{
    if (delayedCallTimer == NULL) return;
    esp_timer_stop(delayedCallTimer);
    esp_timer_delete(delayedCallTimer);
    delayedCallTimer = NULL;
}

// delay is in seconds
static void CallWithDelay(void (*function)(void*),uint16_t delay)
{
    CallWithDelayCancel();

    const esp_timer_create_args_t DelayedTimerArgs = {function, 0, {}, 0, 0};
    ESP_ERROR_CHECK(esp_timer_create(&DelayedTimerArgs, &delayedCallTimer));

    esp_timer_start_once(delayedCallTimer, ((uint64_t)delay)*1000000);
}



static void save_ota_started(bool is_started)
{
    nvs_handle save_handle;
    ESP_ERROR_CHECK(nvs_open("settings", NVS_READWRITE, &save_handle));
    ESP_ERROR_CHECK(nvs_set_u8(save_handle, "ota_start", (uint8_t)is_started));
    ESP_ERROR_CHECK(nvs_commit(save_handle));
    nvs_close(save_handle);
    ESP_LOGI(TAG,"ota started saved: %i", is_started);
}



static bool load_ota_started()
{
    nvs_handle load_handle;
    esp_err_t err = nvs_open("settings", NVS_READWRITE, &load_handle);

    if (err != ESP_OK) { 
        ESP_LOGI(TAG, "    NVS open failed");
        return 0;
    }

    uint8_t ld;
    err = nvs_get_u8 (load_handle, "ota_start", &ld);
    if (err == ESP_ERR_NVS_NOT_FOUND || err == ESP_ERR_NVS_INVALID_NAME)
    {
        nvs_set_u8 (load_handle, "ota_start", 0);
        ld = 0;
        ESP_ERROR_CHECK(nvs_commit(load_handle));
    }

    nvs_close(load_handle);
    ESP_LOGI(TAG,"ota start loaded: %i", ld);

    return ld;
}



#define OTA_ERR_ID_SHIFT 20
static void ota_event_handler (void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data)
{
    using namespace bsh_sys::ota;
    using namespace bsh_sys::mqtt_protocol;

    switch (event_id)
    {
    case OTA_EVT_STARTED:
        bsh_sys::mqtt_helpers::update_ota_progress(1);
        bsh_sys::telem_storage::push_single_block(config->actors_qty + FUP);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_ERR, TAG, "OTA started", NULL, 0);
        break;

    case OTA_EVT_PERCENT:
        {
        int progress = *((int *)event_data);
        ESP_LOGI (TAG," ota %i %% ", progress);
        bsh_sys::mqtt_helpers::update_ota_progress(progress);

        if (progress == 2 || progress == 25 || progress == 50 || progress == 75 || progress == 100)
        {
            bsh_sys::telem_storage::push_telemetry(bsh_sys::mqtt_protocol::ORN_DEVICE, NULL);
        }

        status_arr_for_ble[B_ST_OTA_PROGRESS] = (uint8_t)progress;

        if(progress % 10 == 0) bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "OTA progress %i %", NULL, 0, progress);

        }
        break;

    case OTA_EVT_COMPLETED:
        bsh_sys::mqtt_helpers::update_ota_progress(0);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "OTA completed", NULL, 0);
        break;
    case OTA_EVT_FAILED:
    case OTA_EVT_HTTP_MAX_REDIRECT:
    case OTA_EVT_HTTP_ERR_CONNECT:         
    case OTA_EVT_HTTP_ERR_WRITE_DATA:
    case OTA_EVT_HTTP_ERR_FETCH_HEADER:     
    case OTA_EVT_HTTP_ERR_INVALID_TRANSPORT:
    case OTA_EVT_HTTP_ERR_CONNECTING:
    case OTA_EVT_HTTP_ERR_EAGAIN:       
    case OTA_EVT_HTTP_ERR_CONNECTION_CLOSED:
        bsh_sys::mqtt_helpers::update_ota_progress(0);
        bsh_sys::mqtt_protocol::send_err_message_to_broker(OTA_ERR_CANT_GET_FIRMWARE);
        break;
    
    case OTA_EVT_ERR_DOWNLOAD_CRASHED:
        bsh_sys::mqtt_helpers::update_ota_progress(0);
        bsh_sys::mqtt_protocol::send_err_message_to_broker(OTA_ERR_DOWNLOAD);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, TAG, "OTA failed, download err", NULL, 0);
        break;
    case OTA_EVT_ERR_WRONG_FIRMWARE:
        bsh_sys::mqtt_helpers::update_ota_progress(0);
        bsh_sys::mqtt_protocol::send_err_message_to_broker(OTA_ERR_WRONG_FIRMWARE);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, TAG, "OTA failed, wrong firmware", NULL, 0);
        break;
    case OTA_EVT_ERR_WRONG_LINK:
        bsh_sys::mqtt_helpers::update_ota_progress(0);
        bsh_sys::mqtt_protocol::send_err_message_to_broker(OTA_ERR_CANT_GET_FIRMWARE);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, TAG, "OTA failed, wrong link", NULL, 0); 
        break;

    default:
        ESP_LOGE (TAG,"OTA unknown error: %lu", event_id);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, TAG, "OTA failed", NULL, 0); 
        break;
    }
}


// this function is a callback for regualar rssi sends
static void send_rssi_cb(int8_t rssi)
{
    bool send = false;
    
    if (rssi == 0) return;  // no data
    
    // send to mqtt only every other time
    // coz updates come too ofter - for BLE
    send = !send;
    if (send) bsh_sys::mqtt_helpers::update_wifi_power(rssi); 

    status_arr_for_ble[B_ST_WIFI_RSSI] = 0xFF - rssi;
}



static void ble_shut_down_cb(void)
{
    // CallWithDelay(delayed_wifi_power_saving, 2);
    ESP_LOGI(TAG,"ble shut down.  wifi power saving mode set to WIFI_PS_NONE");
    wifi::wifi_set_power_saving_mode(WIFI_PS_NONE);
}




static void set_log_levels()
{
    esp_log_level_set("-uart-", UART_LOG_LEVEL);
    esp_log_level_set("-uart_raw_out-", UART_RAW_OUT);
    esp_log_level_set("-uart_raw_in-", UART_RAW_IN);
    esp_log_level_set("- ble -", BLE_LOG_LEVEL);
    esp_log_level_set("-mqtt task-", MQTT_LOG_LEVEL);
    esp_log_level_set("- wifi -", WIFI_LOG_LEVEL);
    esp_log_level_set("-mqtt logs-", MQTT_LOGS_LOG_LEVEL);
}

} // bsh_sys namespace