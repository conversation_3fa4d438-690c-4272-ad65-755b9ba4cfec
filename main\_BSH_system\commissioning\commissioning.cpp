#include <stdint.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "nvs.h"
#include "esp_log.h"

#include "device_id.h"
#include "commissioning.h"
#include "mqtt_logs.h"




/* ======= local defines for constants =========*/
#define free_pointer_if_not_null(P) if (P != NULL) {free(P); P = NULL;}
#define return_if_ptr_is_null(P) if (P == NULL) {ESP_LOGW(TAG,"null ptr"); return;}
static const char *TAG = "-sys-";


namespace bsh_sys::commiss{



/* ======= local object declarations =========*/
// mqtt credentials
// у нас в бэке:
// - userId это телефон пользователя
// - clientId это уникальный идентификатор пользователя (в прошивке его вообще нет)
// - deviceId это уникальный идентификатор устройства

static char *mqtt_uid = NULL;
static char *mqtt_passw = NULL;
static char mqtt_dev_id_as_string[21];

// balancer
static char *balancer_link = NULL;

// wifi credentials
static char *wifi_ssid = NULL;
static char *wifi_psw = NULL;



/* ======= local function declarations ========= */
static bool load_credentials();
static bool save_parameter (const char *nvs_name, 
                                const char *parameter_name, const char *parameter_value, 
                                const char *length_name, uint16_t parameter_length);
static bool load_parameter_string (const char *nvs_name, 
                            const char *parameter_name, char **where_to_write,  
                            const char *length_name);     
                        



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
void init ()
{
    ESP_LOGI (TAG,"Commissioning init...");
    load_credentials();
    ESP_LOGI (TAG,"Comm init done.");
    return;
}




// ============= WIFI ====================
void set_wifi_ssid(char *ssid)
{
    return_if_ptr_is_null(ssid);

    free_pointer_if_not_null(wifi_ssid);
    wifi_ssid = (char *)malloc(strlen(ssid) +1);
    strcpy (wifi_ssid, ssid);
    save_parameter ("sys_cfg", "ssid", wifi_ssid, "ssid_l", strlen(ssid) +1);
    bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "WiFi ssid is set", NULL, 0);
}



void set_wifi_passw (char *psw)
{
    return_if_ptr_is_null(psw);

    free_pointer_if_not_null(wifi_psw);
    wifi_psw = (char *)malloc(strlen(psw)+1);
    strcpy (wifi_psw, psw);
    save_parameter ("sys_cfg", "wifi_ps", wifi_psw, "wifi_p_l", strlen(psw)+1);
    bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "WiFi password is set", NULL, 0);
}



char *get_wifi_ssid()
{
    return wifi_ssid;
}



char *get_wifi_passw()
{
    return wifi_psw;
}



// ============= MQTT ====================
void set_mqtt_passw(char *passw)
{
    return_if_ptr_is_null(passw);

    free_pointer_if_not_null(mqtt_passw);
    mqtt_passw = (char *)malloc(strlen(passw)+1);
    strcpy (mqtt_passw, passw);
    save_parameter ("sys_cfg", "mqtt_passw", mqtt_passw, "mqtt_p_l", strlen(passw) + 1);
    bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "mqtt password is set to mqtt_passw", NULL, 0);
}



void set_mqtt_user_id(char *id)  
{
    return_if_ptr_is_null(id);

    free_pointer_if_not_null(mqtt_uid);
    mqtt_uid = (char *) malloc(strlen(id)+1); 
    strcpy (mqtt_uid, id);
    save_parameter("sys_cfg","uid", mqtt_uid, "uid_l", strlen(id) + 1);
    bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "mqtt user id is set to %s", NULL, 0, mqtt_uid);
}



char *get_mqtt_user_id()
{
    return mqtt_uid;
}



char *get_mqtt_passw()
{
    return mqtt_passw;
}



char *get_dev_id_as_string()
{
    uint8_t buff[10] = {};
    bsh_sys::devid::write_device_id_to_buffer(buff);

    for (size_t i = 0; i < 10; i++)
    {
        snprintf (mqtt_dev_id_as_string + i*2, 3 , "%02x", buff[i]);
    }
    mqtt_dev_id_as_string[20] = '\0';
    ESP_LOGI(TAG,"ID converted: %s", mqtt_dev_id_as_string);
    return mqtt_dev_id_as_string;
}



// ============= Balancer ====================
void set_balancer_link(char* link)
{
    return_if_ptr_is_null(link);

    free_pointer_if_not_null(balancer_link);
    balancer_link = (char *)malloc(strlen(link) + 1); 
    strcpy (balancer_link, link);

    save_parameter ("sys_cfg", "blc_link", balancer_link, "blc_l_l", strlen(link) + 1);
    bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "balancer link is set to %s", NULL, 0, balancer_link);
}



char *get_balancerlink()
{ 
    return balancer_link;
}



/*===============================================*\
 * Local function definitions
\*===============================================*/
static bool load_credentials()
{
    ESP_LOGI (TAG,"    Loading credentials...");

    bool loaded_ok = true;

    // load wifi ssid
    loaded_ok &= load_parameter_string ("sys_cfg", "ssid", &wifi_ssid, "ssid_l");

    // load wifi passwd
    loaded_ok &= load_parameter_string ("sys_cfg", "wifi_ps", &wifi_psw, "wifi_p_l");

    // load balancer link
    loaded_ok &=  load_parameter_string("sys_cfg", "blc_link", &balancer_link, "blc_l_l");
    
    // load user id (for server login.  = user cell phone N)
    loaded_ok &= load_parameter_string ("sys_cfg", "uid", &mqtt_uid, "uid_l");

    // load mqtt password 
    loaded_ok &= load_parameter_string ("sys_cfg", "mqtt_passw", &mqtt_passw, "mqtt_p_l");
    ESP_LOGI (TAG,"    Loading done.");

    return loaded_ok;
}



static bool load_parameter_string (const char *nvs_name, 
                            const char *parameter_name, char **where_to_write,  
                            const char *length_name)
{
    nvs_handle _handle;
    esp_err_t err;
    
    err = nvs_open(nvs_name, NVS_READONLY, &_handle);
    if (err == ESP_ERR_NVS_NOT_FOUND) 
    {
        ESP_LOGW (TAG,"    NVS not found");
        return false;
    }

    uint16_t string_length;
    err = nvs_get_u16(_handle, length_name, &string_length);
    size_t lgh = string_length;
    if (err != ESP_OK) 
    {
        ESP_LOGW(TAG, "    cant load %s length", parameter_name);
        return false;
    } else {
        ESP_LOGI(TAG, "    loaded %s length: %u:", parameter_name, string_length);
        free_pointer_if_not_null(*where_to_write);
        *where_to_write = (char *)malloc(string_length + 1);
        err = nvs_get_blob(_handle, parameter_name, *where_to_write, &lgh);
        if (err != ESP_OK) {
            ESP_LOGW(TAG, "    cant load %s. err: %u", parameter_name, err);
            free_pointer_if_not_null(*where_to_write);
            *where_to_write = NULL;
            return false;
        } else {
            ESP_LOGI(TAG, "    loaded %s", parameter_name);
            ESP_LOG_BUFFER_HEXDUMP(TAG, *where_to_write, string_length, ESP_LOG_INFO);
            return true;
        }
    }

    nvs_close(_handle);
}



static bool save_parameter (const char *nvs_name, 
                                const char *parameter_name, const char *parameter_value, 
                                const char *length_name, uint16_t parameter_length)
{
    nvs_handle _handle;
    esp_err_t err;
    ESP_ERROR_CHECK(nvs_open(nvs_name, NVS_READWRITE, &_handle));

    // save length
    err = nvs_set_u16 (_handle, length_name, parameter_length);
    if (err != ESP_OK) {
        ESP_LOGE (TAG, "can't save %s length", parameter_name);
        return false;
    } 

    // save parameter
    err = nvs_set_blob(_handle, parameter_name, parameter_value, parameter_length);
    if (err != ESP_OK) {
        ESP_LOGE (TAG, "can't save %s. length: %u  err: %u", parameter_name, parameter_length, err);
        return false;  
    } else {
        ESP_LOGI (TAG, "saved %s", parameter_name);
        ESP_LOG_BUFFER_HEXDUMP(TAG, parameter_value, parameter_length, ESP_LOG_INFO);
    }
    ESP_ERROR_CHECK(nvs_commit(_handle));
    nvs_close(_handle);
    return true;
}



static void device_id_as_string(char *out_device_id)
{
    uint8_t buff[10] = {};
    bsh_sys::devid::write_device_id_to_buffer(buff);

    for (size_t i = 0; i < 10; i++)
    {
        snprintf (out_device_id + i*2, 3 , "%02x", buff[i]);
    }
    out_device_id[20] = '\0';
    ESP_LOGI(TAG,"ID converted: %s", out_device_id);
}



} // bsh_sys_commiss namespace