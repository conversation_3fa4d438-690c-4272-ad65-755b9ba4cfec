namespace bsh_sys::mqtt_logs::logs_buffer {



int init_buffer(int buffer_size);
void deinit_buffer();



/**
 * Pushes string-message to fifo buffer.
 * @param message expected to be null terminated string
 * 
 * @return 0 if no errors. 1 if buffer is full - not an err- circle buffer logic.
 *      new message will erase last message in the buffer
*/
int push_message(const char *message);



/**
 * Prepares message from provided parameters,
 * and pushed to fifo byffer.
*/
int push_formatted_message(uint8_t log_level, const char *module_tag, const char *message, uint8_t *hex_dump, uint16_t dump_size, va_list argp);




/**
 * Returns pointer to first message to be sent (fifo buffer)
 * Returns NULL if no messages available
 * !!! calling it one more time makes results of prev call invalid !!!
*/
char * get_message();

bool has_message();



/**
 * Returns % of how full is buffer
 * 
 * !! not implemented
*/
int get_buffer_percentage();



}    // end of bsh_sys::mqtt_logs::logs_buffer