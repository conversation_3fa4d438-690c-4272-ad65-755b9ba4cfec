// see https://github.com/espressif/mbedtls
// check  https://travistidwell.com/jsencrypt/demo/ to test encryption

#pragma once

#define SHA256_HASH_SIZE 32     /* SHA-256 output is always 256 bit = 32 bytes */
#define SIGNATURE_SIZE  256     // equals RSA key size : 2048 bits RSA alorythm gives 2048 bits = 256 bytes



namespace bsh_sys::rsa {



typedef struct
{
    unsigned char hash[SHA256_HASH_SIZE];   
} sha_256_hash_t; 


bool init();
void deinit();
bool get_init_status();



/**
 * @brief Prepares data signature: Calculates sha256 hash and signs it with private key
 * 
 * @param data   pointer to data
 * @param data_size  size of data
 * @param signature_buff  buffer to write signature to. size should be SIGNATURE_SIZE
 * 
 * @return 0 on success, or a specific error code.
 */
int sign_data(const unsigned char *data, int data_size, unsigned char *signature_buff);



/**
 * @brief Verifies message signature. Uses sha256 hash. Signature assumed SIGNATURE_SIZE length
 * 
 * @param msg        pointer to message
 * @param msg_size   message size
 * @param signature  pointer to signature. length is assumed = SIGNATURE_SIZE
 */ 
int verify_signature (const unsigned char *msg, int msg_size, const unsigned char *signature);



/**
 * @brief           Encrypts data. Uses rsa module hardcoded !public! key.
 * This is NOT FINALIZED, as there are no checks for data length (RSA has limitations)
 *
 * @param data_to_encrypt       Pointer to data to encrypt.
 * @param data_length           Data length.
 * @param result_buf            Pointer to result buffer. Shall be no less than SIGNATURE_SIZE
 *
 * @return          0 on success, or a specific error code.
 */
int encrypt_data(const char *data_to_encrypt, int data_length, unsigned char *result_buf);



/**
 * @brief Decrypts data with RSA module hardcoded !private! key
 * 
 * @param input_string data to encrypt
 * @param input_length data length
 * @param output   output buffer
 * @param out_buff_size output buffer size. shall be >= SIGNATURE_SIZE
 * 
 * @return          0 on success, or a specific error code.
 */ 
 int decrypt_data (const unsigned char *input_string, int input_length, unsigned char *output, size_t out_buff_size);
 


/**
 * @brief      Calculates sha256 hash
 *
 * @param input_string       Pointer to input string.
 * @param input_length     Input data length.
 * @param output      Pointer to result buffer.
 *
 * @return          0 on success, or a specific error code.
 */
int calculate_sha256_hash(const unsigned char *input_string, int input_length, sha_256_hash_t *output);



/**
 * @brief       Compares two sha256 hashes
 */ 
bool compare_sha256_hashes (char *hash1, char *hash2);



uint32_t get_random();



} // bsh_sys::rsa namespace