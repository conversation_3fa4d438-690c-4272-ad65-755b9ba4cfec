# docker run --rm -v $PWD:/project -w /project -u $UID -e HOME=/tmp espressif/idf:release-v4.4 idf.py build

docker run --rm --interactive -v $PWD:/project -w /project -u $UID -e HOME=/tmp espressif/idf:release-v4.4 /bin/bash -s <<EOF 
idf.py build
cd build
esptool.py --chip esp32-s3 merge_bin -o merged_flash_image.bin @flash_args
cd ..
rm -r build-artifacts
mkdir build-artifacts
cp ./build/A630.bin ./build-artifacts/A630.bin
cp ./build/ota_data_initial.bin ./build-artifacts/ota_data_initial.bin
cp ./build/bootloader/bootloader.bin ./build-artifacts/bootloader.bin
cp ./build/partition_table/partition-table.bin ./build-artifacts/partition-table.bin
cp ./build/merged_flash_image.bin ./build-artifacts/merged_flash_image.bin
EOF