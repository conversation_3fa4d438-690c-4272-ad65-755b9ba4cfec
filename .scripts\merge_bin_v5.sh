docker run --rm -v $PWD:/project -w /project/build -u $UID -e HOME=/tmp espressif/esp-matter:latest_idf_v5.2.3 esptool.py --chip esp32-s3 merge_bin -o merged_flash_image.bin @../flash_args
rm -r build-artifacts
mkdir build-artifacts
cp ./build/A630.bin ./build-artifacts/A630.bin
cp ./build/ota_data_initial.bin ./build-artifacts/ota_data_initial.bin
cp ./build/bootloader/bootloader.bin ./build-artifacts/bootloader.bin
cp ./build/partition_table/partition-table.bin ./build-artifacts/partition-table.bin
cp ./build/merged_flash_image.bin ./build-artifacts/merged_flash_image.bin
cp ./build/nvs.bin ./build-artifacts/nvs.bin
