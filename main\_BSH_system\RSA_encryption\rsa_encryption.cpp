// to print result in form of hex view
// mbedtls_fprintf( f, "%02X%s", buf[i], ( i + 1 ) % 16 == 0 ? "\r\n" : " " );

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <stdint.h>

#include "mbedtls/error.h"
#include "mbedtls/pk.h"
#include "mbedtls/entropy.h"
#include "mbedtls/ctr_drbg.h"
#include "mbedtls/sha256.h" /* SHA-256 only */
#include "mbedtls/md.h"     /* generic interface */
#include "esp_random.h"

#include "esp_log.h"

#include "rsa_encryption.h"
#include "mqtt_logs.h"



namespace bsh_sys::rsa {



/* local objects declarations -----------------------------------------------*/
static const char *TAG = "-RSA enc-";
static bool initialized = false;
mbedtls_pk_context sign_key;
mbedtls_pk_context verify_key;
mbedtls_entropy_context entropy;
mbedtls_ctr_drbg_context ctr_drbg;



/* local functions declarations -----------------------------------------------*/
extern const unsigned char RSA_public_key_start[]   asm("_binary_esp32_rsa_pub_pair_start"); 
extern const unsigned char RSA_public_key_end[]   asm("_binary_esp32_rsa_pub_pair_end");
extern const unsigned char RSA_private_key_start[]   asm("_binary_esp32_rsa_priv_pair_start");
extern const unsigned char RSA_private_key_end[]   asm("_binary_esp32_rsa_priv_pair_end");



/*=================================================================*\
 * exported function definitions
\*=================================================================*/
bool init()
{
    const char *pers = "mbedtls_pk_encrypt";
    int ret = 1;

    ESP_LOGI (TAG,"initializing...");

    if (initialized) 
    {
        ESP_LOGW(TAG,"already initialized");
        return true;
    }

    mbedtls_ctr_drbg_init( &ctr_drbg );
    mbedtls_entropy_init( &entropy );
    mbedtls_pk_init( &sign_key );
    mbedtls_pk_init( &verify_key );

    ESP_LOGI (TAG,"Seeding the random number generator...");
    if( ( ret = mbedtls_ctr_drbg_seed( &ctr_drbg, mbedtls_entropy_func,
                                       &entropy, (const unsigned char *) pers,
                                       strlen( pers ) ) ) != 0 )
    {
        ESP_LOGE (TAG, " failed - mbedtls_ctr_drbg_seed returned -0x%04x\n",
                        (unsigned int) -ret );
        deinit();
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_ERR, TAG, "init failure", NULL, 0);
        return false;
    }
    ESP_LOGI (TAG,"Done");

    // getting pub - decrypt key
    ESP_LOGI(TAG,"parsing verify (public) key...");
    ret = mbedtls_pk_parse_public_key( &verify_key, RSA_public_key_start, 
            RSA_public_key_end - RSA_public_key_start);
    printf("%u \n \n", RSA_public_key_start[0]);
    if (ret != 0)
    {
        ESP_LOGE(TAG,"can't parse verify (public) RSA key 0x%04x",(unsigned int) -ret );
        deinit();
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_ERR, TAG, "keys parsing err", NULL, 0);
        return false;
    }
    ESP_LOGI (TAG,"Done");

    // getting priv - encrypt key
    ESP_LOGI(TAG,"parsing sign (private) key...");
    ESP_LOGI(TAG,"key length: %u",RSA_private_key_end - RSA_private_key_start);
    if ((ret = mbedtls_pk_parse_key( &sign_key, RSA_private_key_start, 
            RSA_private_key_end - RSA_private_key_start, NULL,0, mbedtls_ctr_drbg_random, &ctr_drbg )) != 0)
    {
        ESP_LOGE(TAG,"can't parse sign (private) RSA key 0x%04x",(unsigned int) -ret );
        deinit();
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_ERR, TAG, "keys parsing err", NULL, 0);
        return false;
    }
    ESP_LOGI (TAG,"Done");


    ESP_LOGI(TAG,"initialized successfully");
    initialized = true;
    return true;
}



void deinit()
{
    mbedtls_pk_free( &sign_key );
    mbedtls_pk_free( &verify_key );
    mbedtls_entropy_free( &entropy );
    mbedtls_ctr_drbg_free( &ctr_drbg );

    initialized = false;
    ESP_LOGI(TAG,"de initialized");
}



bool get_init_status()
{
    return initialized;
}



int sign_data(const unsigned char *data, int data_size, unsigned char *signature_buff)
{
    if (!initialized){
        ESP_LOGE (TAG, "must be initialized before using");
        return -1;
    }
    sha_256_hash_t hs;
    int ret = 1;
    size_t sig_size;

    ret = calculate_sha256_hash(data, data_size, &hs);
    if (ret != 0) 
    {
        ESP_LOGE (TAG, "hash generating err %u", ret);
        return ret;
    }

    ESP_LOGI (TAG, "encrypting: ");
    ESP_LOG_BUFFER_HEXDUMP(TAG, &(hs.hash[0]), sizeof(sha_256_hash_t), ESP_LOG_WARN);
    ret = mbedtls_pk_sign(&sign_key, MBEDTLS_MD_SHA256, &(hs.hash[0]), 0, signature_buff , SIGNATURE_SIZE, &sig_size, 
                            mbedtls_ctr_drbg_random, &ctr_drbg);

    if (ret != 0) {ESP_LOGE (TAG,"cant sign message. err: 0x%04x", (unsigned int) -ret);}
    else {ESP_LOGI (TAG,"sign generated");}

    ESP_LOG_BUFFER_HEXDUMP(TAG, signature_buff, SIGNATURE_SIZE, ESP_LOG_WARN);

    return ret;
}



int verify_signature (const unsigned char *msg, int msg_size, const unsigned char *signature)
{
    if (!initialized){
        ESP_LOGE (TAG, "must be initialized before using");
        return -1;
    }
    // calculate sha256 hash
    sha_256_hash_t pct_hash;
    int ret =  calculate_sha256_hash(msg, msg_size, &pct_hash);
    if (ret != 0)
    {
        ESP_LOGW(TAG,"error while getting hash.  err = %u", ret);
        return -1;
    }

    ret = mbedtls_pk_verify(&verify_key, MBEDTLS_MD_SHA256, 
                        (unsigned char *)&pct_hash, SHA256_HASH_SIZE, 
                        signature, SIGNATURE_SIZE);

    if (ret != 0) ESP_LOGW(TAG,"error while veryfing signature.  err = %u", ret);

    return ret;
}



int encrypt_data(const char *data_to_encrypt, int data_length, unsigned char *result_buf)
{
    if (!initialized) 
    {
        ESP_LOGE (TAG,"not initialized");
        return -1;
    }

    int ret = 1;
    size_t output_len = 0;

    // Calculate the RSA encryption.
    ESP_LOGI(TAG, "Generating the encrypted value");
    if( ( ret = mbedtls_pk_encrypt( &verify_key, (const unsigned char *)data_to_encrypt, data_length,
                            result_buf, &output_len, SIGNATURE_SIZE,
                            mbedtls_ctr_drbg_random, &ctr_drbg ) ) != 0 )
    {
        ESP_LOGE(TAG, " failed  ! mbedtls_pk_encrypt returned -0x%04x\n",
                        (unsigned int) -ret );
        return ret;
    }

    ESP_LOGI (TAG,"encrypting result: (%u bytes)", output_len);
    ESP_LOG_BUFFER_HEXDUMP(TAG, result_buf, output_len, ESP_LOG_INFO);

    return 0;
}




int decrypt_data (const unsigned char *input_string, int input_length, unsigned char *output, size_t out_buff_size)
{
    int ret;
    size_t output_len = 0;

    ret = mbedtls_pk_decrypt(&sign_key,
                input_string, input_length,
                output, &output_len, out_buff_size,
                mbedtls_ctr_drbg_random, &ctr_drbg);
    
    if (ret != 0) {ESP_LOGW (TAG,"cant decrypt message. err: 0x%04x", (unsigned int) -ret);}
    else {ESP_LOGI (TAG,"message decrypted");}

    ESP_LOG_BUFFER_HEXDUMP(TAG, output, output_len, ESP_LOG_INFO);

    return ret;
}



uint32_t get_random()
{
    return esp_random();
}



int calculate_sha256_hash(const unsigned char *input_string, int input_length, sha_256_hash_t *output)
{
    // https://github.com/ARMmbed/mbed-os-example-tls/blob/master/hashing/main.cpp
    // example

    // mbedtls_sha256(hello_buffer, hello_len, output1, 0);    deprecated
    int ret;

    ESP_LOGI (TAG, "sha256 hash generated. data len = %u", input_length);
    ret = mbedtls_sha256(input_string,
                    input_length,
                    (unsigned char *)&(output->hash),
                    0);

    ESP_LOG_BUFFER_HEXDUMP(TAG, output, sizeof(sha_256_hash_t), ESP_LOG_WARN);
    
    return ret;
}



bool compare_sha256_hashes (char *hash1, char *hash2)
{
    for (size_t i = 0; i < SHA256_HASH_SIZE; i++)
    {
        if (*(hash1 + i) != *(hash2 + i)) return false;
    }
    return true;
}

/*=================================================================*\
 * local function declarations
\*=================================================================*/



}  //bsh_sys::rsa namespace


