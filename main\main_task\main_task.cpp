#include <stddef.h>
#include <string.h>

#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "esp_err.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "nvs_flash.h"
#include "esp_err.h"
#include "esp_ota_ops.h"

#include "main_task.h"
#include "uart_protocol.h"
#include "telemetry_filter.h"
#include "queues_helpers.h"
#include "device_specific_data.h"
#include "system.h"
#include "emulator.h"
#include "LOG_LEVELS.h"
#include "mqtt_logs.h"



namespace a630::main_task {



#define QUERY_ALL_PERIOD   15 //s
#define HEARTBEAT_PERIOD   17 //s

#define DELAY_FOR_BCT 800 //ms
#define QUEARY_ALL_DELAY 1500000  //us



typedef enum {
    MONDAY,
    TUESDAY,
    WENSDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY,
    SUNDAY,
} week_day_t;  // for aroma repeating patterns (FSR)



/* ======= local defines for constants =========*/
static const char *TAG = "-main task-";
static uint32_t time_from_query_all = esp_timer_get_time();



/* ======= local object declarations =========*/
static bool init_done = false;
#if EMULATOR_ON == 1
static bool emulator_on = true;
#else
static bool emulator_on = false;
#endif

static bool device_info_received = false;
static esp_timer_handle_t regular_seconds_calls_timer = NULL;
static bsh_sys::system_config_t sys_config;

// !! big
static uint8_t pkts_buffer[365] = {};   // accumulates splitted incoming uart packets - for parsing



/* ======= local function declarations ========= */
static void main_task_init();
static void smart_home_conn_events_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data);
static void ble_commands_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data);

// mqtt
static void treat_mqtt_incoming_blocks();

// uart
static void uart_incoming_data_cb(const uint8_t *data, uint16_t data_len);
static void act_on_packet(a630::uart_protocol::tuya_uart_packet_t *pkt);
static void regular_seconds_calls_timer_handler(void *);

// helpers
static void mqtt_actor_to_tuya_dp(const bsh_sys::mqtt_protocol::mqtt_in_block_t *mqtt_cmd ,a630::uart_protocol::tuya_data_point_t *dp_out);
static uint32_t to_big_endian_4_bytes(uint32_t value);
static a630::uart_protocol::tuya_network_status_t get_network_status_in_tuya_format();
static void add_day_to_string(char *days_string, week_day_t day);

static void save_auto_or_manual_mode(bool mode);
static bool load_a_m_mode();



/*================================================================*\
 * Exported functions definitions
\*================================================================*/



void main_task(void *args)
{

    if (!init_done) 
    {
        main_task_init();
        esp_timer_start_periodic(regular_seconds_calls_timer, 1000000);
    }

    while (1)
    {
        treat_mqtt_incoming_blocks();
        vTaskDelay (pdMS_TO_TICKS(20)); // 0 allows to run tasks with same priority
    }
    
}



/*================================================================*\
 * Local function definitions
\*================================================================*/
static void main_task_init()
{
    ESP_LOGI (TAG, "Main task init..");

    a630::main_task::queue_helpers::init();

    // hardware init 
    bsh_sys::uart::uart_settings_t uart_config = {
        .sending_queue = a630::main_task::queue_helpers::get_uart_queue(),
        .uart_config = {},
        .uart_N = UART_NUM,
        .tx_pin = UART_TX_PIN,
        .rx_pin = UART_RX_PIN,
        .rts_pin = UART_RTS_PIN,
        .cts_pin = UART_CTS_PIN,
        .incoming_data_cb = uart_incoming_data_cb,
    };
    memcpy (&uart_config.uart_config, &uart_cnf, sizeof(uart_config_t));

    sys_config.firware_version_major = FIRMWARE_VERSION_MAJOR,
    sys_config.firmware_version_minor = FIRMWARE_VERSION_MINOR,
    sys_config.firmware_version_patch = FIRMWARE_VERSION_PATCH,
    memcpy(sys_config.commit_hash, esp_ota_get_app_description()->version, 7);
    sys_config.ota_hardcoded_link = HARDCODED_OTA_LINK,
    sys_config.uart_stt = &uart_config,
    sys_config.actors_qty = a630::mqtt_protocol::ACTORS_QTY,
    sys_config.guid_prefix = MQTT_DEVICE_GUID_PREFIX,
    sys_config.ble_device_name = BLE_DEVICE_NAME,
    sys_config.default_wifi_login = DEFAULT_WIFI_LOGIN;
    sys_config.default_wifi_passw = DEFAULT_WIFI_PASSW;
    sys_config.ble_device_id_code = DEVICE_ID_CODE;
    memcpy (sys_config.ble_device_type, ble_dev_type, 4);

    bsh_sys::init(&sys_config); 
    ESP_ERROR_CHECK(esp_event_handler_register_with(bsh_sys::get_event_loop_handle(), bsh_sys::SMART_HOME_CONN_EVENTS, ESP_EVENT_ANY_ID, smart_home_conn_events_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register_with(bsh_sys::get_event_loop_handle(), bsh_sys::BLE_COMMAND_EVENTS, ESP_EVENT_ANY_ID, ble_commands_handler, NULL));
    
    const esp_timer_create_args_t timer_args = {regular_seconds_calls_timer_handler, 0, {}, 0, 0};
	if((esp_timer_create(&timer_args, &regular_seconds_calls_timer)) != ESP_OK) {
        ESP_LOGE(TAG,"cant create timer");
        return;
    }	

    init_done = true;
    ESP_LOGI (TAG, "Init done. \n \n");

    esp_log_level_set("-uart_protocol-", UART_PROTOCOL_LOG_LEVEL);
}



static void smart_home_conn_events_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data)
{
    using namespace bsh_sys;

    ESP_LOGI (TAG, "network status: %li", event_id);

    if (event_id == BSH_SMART_HOME_CONNECTED)
    {
        ESP_LOGI (TAG, "\n \n \n================================\n==== Connected to SH system ====\n================================\n \n \n");
        
        bsh_sys::telem_storage::push_telemetry(bsh_sys::mqtt_protocol::ORN_DEVICE, NULL);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "connected to broker", NULL, 0);
        #if EMULATOR_ON == 1        
            a630::emu::turn_regular_fake_telemetry(true);
        #endif
    }

}


#define START_FUNGENE_OTA 0x01


static void ble_commands_handler(void *event_handler_arg, esp_event_base_t event_base, int32_t event_id, void *event_data)
{
    switch (event_id)
    {
    case START_FUNGENE_OTA:
        a630::main_task::queue_helpers::send_uart_command(a630::main_task::queue_helpers::UART_CMD_START_FNG_OTA);
        break;
    
    default:
        break;
    }
}



static a630::uart_protocol::tuya_network_status_t get_network_status_in_tuya_format()
{
    return a630::main_task::queue_helpers::sys_ntw_status_to_tuya_format(bsh_sys::get_network_status());
}



static void treat_mqtt_incoming_blocks()
{
    using namespace a630::mqtt_protocol;
    static QueueHandle_t mqtt_in_q = bsh_sys::get_mqtt_incoming_queue();
    static QueueHandle_t uart_out_q = a630::main_task::queue_helpers::get_uart_queue();

    bsh_sys::mqtt_protocol::mqtt_in_block_t block;
    BaseType_t res;

    if (esp_timer_get_time() - time_from_query_all < QUEARY_ALL_DELAY ) return;


    res = xQueueReceive(mqtt_in_q, &block, 0);  
    if (res == pdFALSE) 
    {
        return;  // nothing in the queue
    }

    bsh_sys::mqtt_protocol::pckt_integrity_err_events_t r = check_value(&block);
    if (r != bsh_sys::mqtt_protocol::PACKET_IS_OK) 
    {
        bsh_sys::mqtt_protocol::send_err_message_to_broker(r);
        ESP_LOGE (TAG,"wrong command");
        return;
    }

    // fake response if emulator is enabled
    if (emulator_on) 
    {
        ESP_LOGW(TAG,"emulator is on. faking reply.");
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO,TAG, "emulator is on. faking reply", NULL, 0);
        a630::emu::execute_cmd(&block);
        return;
    }
    
    switch (get_actor_id(block.actor))
    {
    case MOD:
    case ALP:
    case FST:
    case FSS:
    case FSR:
    case CLN:
    case ARA:
    case SCN:
    case SLP:
    case STS:
    case STE:
    case STC:
    case WTC:
    case CLR:
    case STA:
    case WLP:
    case FSM:
    case WCN:
        {
        bsh_sys::telem_storage::push_telemetry_after_ms (DELAY_FOR_BCT, bsh_sys::mqtt_protocol::ORN_BROKER_CMD);

        // pass to uart
        a630::uart_protocol::tuya_data_point_t dp;   
        uint8_t tuya_dp_data[20] = {};
        dp.data = tuya_dp_data;
        mqtt_actor_to_tuya_dp(&block, &dp);
        a630::uart_protocol::tuya_out_uart_packet_t tuya_pkt = {};
        a630::uart_protocol::make_single_dp_packet(&tuya_pkt, &dp);

        bsh_sys::uart::uart_out_queue_obj_t uart_out_obj = {};
        uart_out_obj.data_size = a630::uart_protocol::get_tuya_pkt_length(&tuya_pkt);
        memcpy (uart_out_obj.data, &tuya_pkt, uart_out_obj.data_size);

        xQueueSend (uart_out_q, &uart_out_obj, 0);
        }
        break;

        // ============ system commands =============
    case BCT:
        bsh_sys::telem_storage::push_telemetry_after_ms (DELAY_FOR_BCT, bsh_sys::mqtt_protocol::ORN_BROKER_CMD);
        a630::main_task::queue_helpers::send_uart_command(a630::main_task::queue_helpers::UART_CMD_QUERY_ALL);
        time_from_query_all = esp_timer_get_time();
        break;
        
    case FSC:   // FSC stores auto/manual mode setting. its to be set by broker. its not passed to device and not received from device.
        {
            bsh_sys::telem_storage::push_telemetry_after_ms (DELAY_FOR_BCT, bsh_sys::mqtt_protocol::ORN_BROKER_CMD);

            a630::uart_protocol::tuya_data_point_t dp;
            uint8_t tuya_dp_data[5] = {};
            dp.data = tuya_dp_data;
            mqtt_actor_to_tuya_dp(&block, &dp);
            a630::tlm_filter::update_dp(&dp);
            save_auto_or_manual_mode(block.set_value.in_bytes[0]);
        }
        break;
    case FFV:
        break;
    case FFU:
        a630::main_task::queue_helpers::send_uart_command(a630::main_task::queue_helpers::UART_CMD_START_FNG_OTA);
        break;
    case FFS:
        break;

    case ACTORS_QTY:
        break;
    }
}



/** 
 * Should work fast, as it processes uart incoming data, stored in small buffer
 * Device splits big packets into pieces of 120 bytes,
 * some packets are arrived together i nsame uart tranfer session (glued together)
 * So algorythm is:
 * Copy arrived data to buffer. Parse whatever is in buffer. Clear parsed data, left whats not parsed.
 * If packet err accur - look for next packet.
 */

static void uart_incoming_data_cb(const uint8_t *data, uint16_t data_len)
{
    using namespace a630::uart_protocol;

    static uint8_t data_size_in_buffer = 0;
    tuya_uart_packet_t packet;
    uint16_t packets_parsing_shift = 0;  // used when several packets glued together
    parsing_errs_t err;

    // ESP_LOGI (TAG,"\n");
    // ESP_LOGI (TAG,"incoming uart pckt");
    // ESP_LOG_BUFFER_HEX(TAG, data, data_len);
    
    
    // put data in buffer. (append to existing data if any)
    if (sizeof (pkts_buffer) - data_size_in_buffer < data_len) data_size_in_buffer = 0; // if buffer is full, purge it
    if (data[0] == HEADER_0 && data[1] == HEADER_1) data_size_in_buffer = 0; // if new header arrived, purge whats in buffer

    memcpy (pkts_buffer + data_size_in_buffer, data, data_len);
    data_size_in_buffer += data_len;
    // ESP_LOGW (TAG,"packet in. copied. in buff %u", data_size_in_buffer);


    // parse
    do
    {
        err = parse_packet(pkts_buffer + packets_parsing_shift, data_size_in_buffer - packets_parsing_shift, &packet);

        if (err == PCKT_ERR_WRONG_LENGTH) // last whole packet was parsed. move whats left to buffer start.
        {
            for (size_t i = 0; i < data_size_in_buffer - packets_parsing_shift; i++)
            {
                pkts_buffer[i] = pkts_buffer[packets_parsing_shift + i];
            }
            data_size_in_buffer = data_size_in_buffer - packets_parsing_shift;
            // ESP_LOGW (TAG,"small unparsed piece left. data in buffer: %u", data_size_in_buffer);
            break;
        }

        // if error in packet - try to find next header 
        if (err == PCKT_ERR_WRONG_HEADER || err == PCKT_ERR_WRONG_CHECKSUM || err == PCKT_ERR)
        {
            ESP_LOGW (TAG,"err in uart packet: %s", pkt_error_to_string(err));
            uint8_t *next_hdr = find_next_header(pkts_buffer + 1 + packets_parsing_shift, // + 1 to dont find same packet header
                                                packet.data_len - packets_parsing_shift - 1);
            if (next_hdr == NULL) {
                ESP_LOGW (TAG,"no next header"); // data left in buffer is garbage. purge it
                data_size_in_buffer = 0;
                break;
            } else {
                packets_parsing_shift = next_hdr - pkts_buffer;
                ESP_LOGW (TAG,"found next header");
                continue;
            }
        }

        if (err == PCKT_WARN_UNPARCED_DATA_LEFT )
        {
            // if no error - move shift pointer, act on packet and continue
            packets_parsing_shift += packet.data_len + TUYA_PACKET_SERVICE_BYTES_QTY;

            if (packet.command != TY_REPLY) log_uart_packet(&packet);  // reply command is logged in details DP by DP - later in code

            act_on_packet(&packet);
        }

        if (err == PCKT_NO_ERR) // all data in buffer was parsed
        {
            if (packet.command != TY_REPLY) log_uart_packet(&packet);  // reply command is logged in details DP by DP - later in code
            ESP_LOGD (TAG,"everything parsed");

            data_size_in_buffer = 0;
            act_on_packet(&packet);
            break;
        }
    } while (err == PCKT_WARN_UNPARCED_DATA_LEFT);
}



static void act_on_packet(a630::uart_protocol::tuya_uart_packet_t *pkt)
{
    using namespace a630::uart_protocol;

    parsing_errs_t dp_parcing_err;
    tuya_data_point_t data_point;

    switch (pkt->command)
    {
        case TY_HEARTBEAT:
        {
            ESP_LOGI (TAG,"mcu replied to heartbeat");
                
            // send network status. placed here to make sure MCU is on (at startup)
            // a630::main_task::queue_helpers::send_uart_command(a630::main_task::queue_helpers::UART_CMD_NETWORK_STATUS);
        }
        break;
        
        case TY_GET_PRODUCT_INFO:
        {
            ESP_LOGI (TAG,"got product info");
            if (pkt->data_len > 300) 
            {
                ESP_LOGE (TAG,"too big packet");
                return;
            }

            ESP_LOG_BUFFER_HEXDUMP (TAG, pkt->data, pkt->data_len, ESP_LOG_INFO);

            bool firm_id_found = false;
            bool product_id_found = false;
            for (size_t i = 0; i < pkt->data_len; i++)
            {
                if (pkt->data[i] == '"' && pkt->data[i+1] == 'p' && pkt->data[i+2] == '"')
                {
                    char tmp[17] = {};
                    memcpy(tmp, &pkt->data[i+5], 16);
                    a630::tlm_filter::set_fung_product_id(tmp);
                    product_id_found = true;
                    bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "got prouct info: %s", NULL, 0, tmp);
                } 

                if (pkt->data[i] == '"' && pkt->data[i+1] == 'v' && pkt->data[i+2] == '"')
                {
                    // format is ,"v":"1.0.0",
                    uint8_t tmp[3];
                    tmp[0] = pkt->data[i+5] - 0x30;    
                    tmp[1] = pkt->data[i+7] - 0x30;    
                    tmp[2] = pkt->data[i+9] - 0x30;  
                    a630::tlm_filter::update_fung_firm_ver(tmp);

                    // ESP_LOGW (TAG,"got firm ver: %u %u %u", packet.data[i+5], packet.data[i+7], packet.data[i+9]);
                    ESP_LOGI (TAG,"got firm ver: %u %u %u", tmp[0], tmp[1], tmp[2]);
                    bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "got firm ver: %u %u %u", NULL, 0, tmp[0], tmp[1], tmp[2]);
                    firm_id_found = true;          
                } 
            }
            if (firm_id_found && product_id_found) device_info_received = true;
            // send query all packet after getting manufacturing data
            a630::main_task::queue_helpers::send_uart_command(a630::main_task::queue_helpers::UART_CMD_QUERY_ALL);
            time_from_query_all = esp_timer_get_time();
        }
            break;

        case TY_REPLY:
            {
                uint16_t dp_parsing_start_shift = 0;

                // ESP_LOGI (TAG,"DP: mcu reply");
                // could contain multiple data point(units). need to parse through them, and react
                do
                {
                    // ESP_LOGI (TAG,"parsing DP. shift: %u, data_l: %u", dp_parsing_start_shift, packet.data_len);
                    dp_parcing_err = parse_data_point(pkt->data + dp_parsing_start_shift, pkt->data_len, &data_point);

                    if ( dp_parcing_err != PCKT_NO_ERR)
                    {
                        ESP_LOGW (TAG,"err in DP: %i", dp_parcing_err);
                        if (dp_parcing_err == PCKT_WRONG_TYPE)
                        {
                            ESP_LOGW (TAG,"DP: %s   data type: %u", a630::uart_protocol::dp_id_to_string((a630::uart_protocol::data_point_id_t)data_point.dpid), (uint8_t)data_point.data_type);
                            ESP_LOG_BUFFER_HEXDUMP(TAG, pkt->data + dp_parsing_start_shift, pkt->data_len, ESP_LOG_INFO);
                        }
                        return;
                    }

                    log_dp(&data_point);

                    if (is_used_dp(&data_point) && data_point.dpid != a630::uart_protocol::DP_FAST_SMELL_CTRL) a630::tlm_filter::update_dp (&data_point);
                    dp_parsing_start_shift += get_dp_length(&data_point);
                } while (dp_parsing_start_shift < pkt->data_len);
                break;
            }

    case TY_WIFI_POWER:
        {
            a630::main_task::queue_helpers::send_uart_command(a630::main_task::queue_helpers::UART_CMD_SEND_RSSI);
        }
        break;

    case TY_NETWORK_STATUS:
        {
            // it seems device never requests network status, expecting module to send it.
            // also device replied to sent status with same 03 command. so sending status here leads to infinite loop
        }
        break;

    case TY_LOCAL_TIME:
        {
            a630::main_task::queue_helpers::send_uart_command(a630::main_task::queue_helpers::UART_CMD_TIME);
        }
        break;

    // these are for sending TO mcu. should not come back
    case TY_SET:
    case TY_QUERY_ALL:
        ESP_LOGW (TAG,"to_mcu_only packet comes back. cmd:%u", pkt->command);
        break;
    }

}



static void mqtt_actor_to_tuya_dp(const bsh_sys::mqtt_protocol::mqtt_in_block_t *mqtt_cmd ,a630::uart_protocol::tuya_data_point_t *dp_out)
{
    using namespace a630::mqtt_protocol;
    using namespace a630::uart_protocol;
    using namespace bsh_sys::mqtt_protocol;

    actor_t actor_index = get_actor_id(mqtt_cmd->actor);
    switch (mqtt_cmd->value_type)
    {
    case INT:
        if (actor_index == MOD || actor_index == FSC)
        {
            dp_out->data_type = TDT_BOOL;
            dp_out->data_len = 1;
            *dp_out->data = (uint8_t)mqtt_cmd->set_value.in_bytes[0];
        } else if (actor_index == FSR) {
            dp_out->data_type = TDT_STRING;
            char days_string[17] = {};   //in format "1,2,3,4,5,6,7"   no quotes
            uint8_t days = mqtt_cmd->set_value.in_bytes[0];

            if (days & 1<< 6) add_day_to_string(days_string, MONDAY);
            if (days & 1<< 5) add_day_to_string(days_string, TUESDAY);
            if (days & 1<< 4) add_day_to_string(days_string, WENSDAY);
            if (days & 1<< 3) add_day_to_string(days_string, THURSDAY);
            if (days & 1<< 2) add_day_to_string(days_string, FRIDAY);
            if (days & 1<< 1) add_day_to_string(days_string, SATURDAY);
            if (days & 1<< 0) add_day_to_string(days_string, SUNDAY);
            memcpy (dp_out->data, days_string, strlen(days_string));
            dp_out->data_len = strlen(days_string);
        } else {
            dp_out->data_type = TDT_INT;
            dp_out->data_len = 4;
            *(uint32_t *)(dp_out->data) = to_big_endian_4_bytes(mqtt_cmd->set_value.in_int);
        }
        break;

    case FLOAT:
        ESP_LOGE (TAG,"no float type in tuya");
        break;
    case OTA_STRING:
        // this mqtt command is not for device 
        break;
    }


    // set DP ID
    switch (actor_index)
    {
        case MOD: dp_out->dpid = DP_MOD;                break;
        case STA: dp_out->dpid = DP_DEVICE_STATE;       break;
        case ARA: dp_out->dpid = DP_AREA;               break;
        case WLP: dp_out->dpid = DP_OIL_LEVEL;          break;
        case STS: dp_out->dpid = DP_SLEEP_TIME_START;   break;   
        case STE: dp_out->dpid = DP_SLEEP_TIME_END;     break; 
        case SCN: dp_out->dpid = DP_SCENARIO;           break; 
        case FSC: dp_out->dpid = DP_FAST_SMELL_CTRL;    break;
        case FSM: dp_out->dpid = DP_FAST_SMELL;         break; 
        case FST: dp_out->dpid = DP_FAST_SMELL_TIME;    break;
        case FSS: dp_out->dpid = DP_FAST_SMELL_START_TIME; break;
        case FSR: dp_out->dpid = DP_FAST_SMELL_REPEAT;  break;
        case STC: dp_out->dpid = DP_POSTPONE_START;     break;
        case WCN: dp_out->dpid = DP_WORK_COUNT;         break;
        case WTC: dp_out->dpid = DP_WORK_TIME_CD;       break;
        case CLR: dp_out->dpid = DP_CLEAR_DATA;         break;
        case CLN: dp_out->dpid = DP_CLEAN;              break;
        case SLP: dp_out->dpid = DP_POWER_SAVE_MODE;    break;
        case ALP: dp_out->dpid = DP_CONCENTRATION;      break;

        // system commands actors. should not be passed into this function
        case BCT:    
        case FFV:
        case FFU:
        case FFS:
            ESP_LOGE (TAG, "function should not be called for system actors");
            // no action for this actors
            break;

        case ACTORS_QTY:
            // nothing to do
            break;
    }
}



static uint32_t to_big_endian_4_bytes(uint32_t value)
{
    uint32_t result = {};
    uint8_t *r = (uint8_t *)&result; 
    memcpy (r+3, (uint8_t *)&value, 1);
    memcpy (r+2, (uint8_t *)&value + 1, 1);
    memcpy (r+1, (uint8_t *)&value + 2, 1);
    memcpy (r, (uint8_t *)&value + 3, 1);
    return result;
}



/**
 * tuya protocol format: "1,2,3,4,5,6,7" = moday tuesday etc.   NO QUOTES
 */
void add_day_to_string(char *days_string, week_day_t day)
{
    uint8_t add_pos = strlen(days_string);

    if (add_pos != 0) 
    {
        days_string[add_pos] = ',';
        add_pos++;
    }
    switch (day)
    {
    case MONDAY: days_string[add_pos] = '1'; break;
    case TUESDAY: days_string[add_pos] = '2'; break;
    case WENSDAY: days_string[add_pos] = '3'; break;
    case THURSDAY: days_string[add_pos] = '4'; break;
    case FRIDAY: days_string[add_pos] = '5'; break;
    case SATURDAY: days_string[add_pos] = '6'; break;
    case SUNDAY: days_string[add_pos] = '7'; break;
    }
};



static void regular_seconds_calls_timer_handler(void *)
{
    using namespace a630::main_task::queue_helpers;

    static uint16_t second = 0;
    second++;
    static uint32_t mod_was_on = false;

    if (mod_was_on == false)
    {
        if (bsh_sys::telem_storage::get_block(a630::mqtt_protocol::MOD)->value.in_int == true)
        {
            mod_was_on = second;
        }
    }

    switch (second)
    {
    case 1:

         send_uart_command(UART_CMD_SEND_HEARTBEAT);

        {
        // push MOD = 0 to telemetry filter. coz device doesnt send anything sometimes
        a630::uart_protocol::tuya_data_point_t dp;
        a630::uart_protocol::make_set_mode_dp(&dp, false);
        a630::tlm_filter::update_dp(&dp);
        }
        break;
    
    case 2:
        // check if ota was started before reboot
        if (bsh_sys::is_first_start_after_ota())
        {
            a630::uart_protocol::tuya_data_point_t dp;
            a630::uart_protocol::make_set_mode_dp(&dp, true);
            a630::main_task::queue_helpers::send_DPs_to_uart(&dp, 1);
        }

        // set FSC in telemetry filter to loaded value
        // fsc should be never sent to device. its only for from/to broker
        {
        
        bsh_sys::mqtt_protocol::mqtt_in_block_t tmp;
        a630::mqtt_protocol::set_actor(&tmp, a630::mqtt_protocol::FSC);
        tmp.value_type = bsh_sys::mqtt_protocol::INT;
        tmp.set_value.in_bytes[0] = load_a_m_mode();    

        a630::uart_protocol::tuya_data_point_t dp;
        uint8_t buff[4] = {};
        dp.data = buff;
        mqtt_actor_to_tuya_dp(&tmp, &dp);   
        a630::tlm_filter::update_dp(&dp);
        }


        break;

    case 3: // set CLN to 0 at device startup
        {

        bsh_sys::mqtt_protocol::mqtt_out_block_t block = {
            .actor = {'C','L','N'},
            .index = 0,
            .capability = 0,
            .action_type = bsh_sys::mqtt_protocol::REPLY,
            .value_type = bsh_sys::mqtt_protocol::INT,
            .value = {.in_int = 0},
        };

        bsh_sys::telem_storage::update_block(a630::mqtt_protocol::get_actor_id(block.actor), &block);
        }
        break;

    case 10:
        send_uart_command(UART_CMD_REQUEST_PROD_INFO);
        break;

    default:
        break;
    }


    if (mod_was_on)
    {
        if (second == mod_was_on + 1)
        {
            a630::main_task::queue_helpers::send_uart_command(a630::main_task::queue_helpers::UART_CMD_NETWORK_STATUS);
        }

        if (second == mod_was_on + 19)
        {
            a630::main_task::queue_helpers::send_working_time_always_on();
        }

        if (second % 600 == 0)   // send time to mcu every 10 minutes
        {
            if (bsh_sys::date_time::get_date_time())
            {
                a630::main_task::queue_helpers::send_uart_command(a630::main_task::queue_helpers::UART_CMD_TIME);
            }
        }
    }



    if (second % QUERY_ALL_PERIOD == 0)
    {
        time_from_query_all = esp_timer_get_time();
        send_uart_command(UART_CMD_QUERY_ALL);
    }

    if (second % HEARTBEAT_PERIOD == 0)  
    {
        send_uart_command(UART_CMD_SEND_HEARTBEAT);
    }
}



static void save_auto_or_manual_mode(bool mode)
{
    nvs_handle save_handle;
    ESP_ERROR_CHECK(nvs_open("settings", NVS_READWRITE, &save_handle));
    ESP_ERROR_CHECK(nvs_set_u8(save_handle, "a_m_mode", mode));
    ESP_ERROR_CHECK(nvs_commit(save_handle));
    nvs_close(save_handle);
    ESP_LOGI(TAG,"a_m_mode saved: %i", mode);
}



static bool load_a_m_mode()
{
    nvs_handle load_handle;
    esp_err_t err = nvs_open("settings", NVS_READWRITE, &load_handle);

    if (err != ESP_OK) { 
        ESP_LOGI(TAG, "    NVS open failed");
        return false;
    }

    uint8_t ld;
    err = nvs_get_u8 (load_handle, "a_m_mode", &ld);
    if (err == ESP_ERR_NVS_NOT_FOUND || err == ESP_ERR_NVS_INVALID_NAME)
    {
        nvs_set_u8 (load_handle, "a_m_mode", 1);
        ld = 1;
        ESP_ERROR_CHECK(nvs_commit(load_handle));
    }

    nvs_close(load_handle);
    ESP_LOGI(TAG,"a_m_mode loaded: %i", ld);

    return (bool)ld;
}



} // a630::main_task namespace