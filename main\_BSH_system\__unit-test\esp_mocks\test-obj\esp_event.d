test-obj/.//esp_event.o: esp_event.cpp \
 /opt/cpputest/include/CppUTest/MemoryLeakDetectorNewMacros.h \
 /opt/cpputest/include/CppUTest/CppUTestConfig.h \
 /opt/cpputest/include/CppUTest/CppUTestGeneratedConfig.h \
 /opt/cpputest/include/CppUTest/StandardCLibrary.h \
 /opt/cpputest/include/CppUTest/MemoryLeakDetectorMallocMacros.h \
 esp_err.h esp_event.h general_mocks.h
/opt/cpputest/include/CppUTest/MemoryLeakDetectorNewMacros.h:
/opt/cpputest/include/CppUTest/CppUTestConfig.h:
/opt/cpputest/include/CppUTest/CppUTestGeneratedConfig.h:
/opt/cpputest/include/CppUTest/StandardCLibrary.h:
/opt/cpputest/include/CppUTest/MemoryLeakDetectorMallocMacros.h:
esp_err.h:
esp_event.h:
general_mocks.h:
