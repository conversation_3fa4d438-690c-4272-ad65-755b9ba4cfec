
#include "esp_timer.h"
#include "esp_log.h"

#include "emulator.h"
#include "main_task.h"
#include "queues_helpers.h"



/* ======= local defines for constants =========*/
static const char *TAG = "-emu-";
#define TLM_UPDATE_TIME 8    // s

struct 
{
    bool mod = false;   // on/off
    uint8_t sta = 0;
    uint16_t ara = 33;
    uint8_t wlp = 51;
    uint8_t alp = 50;   
    uint16_t sts = 8;
    uint16_t ste = 9;
    bool scn = 0;
    bool fsc = false;
    uint8_t fsm = 0;
    uint16_t fst = 100;
    uint16_t fss = 100;
    uint8_t fsr = 0;    // bitmask for week days
    uint16_t wtc = 10;
    uint16_t stc = 10;
    uint16_t wcn = 14;
    bool clr = false;
    bool cln = false;
    uint8_t slp = 0;
    bool regular_telem = true;
} emulated_state;



namespace a630::emu {



/* ======= local object declarations =========*/
static esp_timer_handle_t telem_timer = NULL;



/* ======= local function declarations ========= */
static void init_timer();
static void telemetry_timer_handler(void *);
static void send_entire_telemetry();



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
void turn_regular_fake_telemetry (bool on_off)
{
    emulated_state.regular_telem = on_off;
    
    if (telem_timer == NULL) init_timer();
    if (on_off) {
        esp_timer_start_periodic (telem_timer, TLM_UPDATE_TIME * 1000000);
    } else {
        esp_timer_stop(telem_timer);
    }
    ESP_LOGW(TAG,"regular telemetry on/off: %u", on_off);
}



void execute_cmd (bsh_sys::mqtt_protocol::mqtt_in_block_t *command)
{
    using namespace bsh_sys::mqtt_protocol;

        mqtt_out_block_t block = {
            .actor = {0,0,0},
            .index = 0,
            .capability = 0,
            .action_type = REPLY,
            .value_type = INT,
            .value = {},
        };
        memcpy (block.actor, command->actor, 3);

        using namespace a630::mqtt_protocol;
        switch (get_actor_id(command->actor))
        {
        // commands to recieve fake response:
        case ALP:
            if (command->action_type == WRITE) emulated_state.alp = command->set_value.in_int;
            block.value.in_int = emulated_state.alp;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case ARA:
            if (command->action_type == WRITE) emulated_state.ara = command->set_value.in_int;
            block.value.in_int = emulated_state.ara;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case CLN:
            if (command->action_type == WRITE) emulated_state.cln = command->set_value.in_int;
            block.value.in_int = emulated_state.cln;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case CLR:
            if (command->action_type == WRITE) emulated_state.clr = command->set_value.in_int;
            block.value.in_int = emulated_state.clr;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case FSC:
            if (command->action_type == WRITE) emulated_state.fsc = command->set_value.in_int;
            block.value.in_int = emulated_state.fsc;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case FSM:
            if (command->action_type == WRITE) emulated_state.fsm = command->set_value.in_int;
            block.value.in_int = emulated_state.fsm;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case FSR:
            if (command->action_type == WRITE) emulated_state.fsr = command->set_value.in_int;
            block.value.in_int = emulated_state.fsr;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case FSS:
            if (command->action_type == WRITE) emulated_state.fss = command->set_value.in_int;
            block.value.in_int = emulated_state.fss;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case FST:
            if (command->action_type == WRITE) emulated_state.fst = command->set_value.in_int;
            block.value.in_int = emulated_state.fst;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case MOD:
            if (command->action_type == WRITE) emulated_state.mod = command->set_value.in_int;
            block.value.in_int = emulated_state.mod;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case SCN:
            if (command->action_type == WRITE) emulated_state.scn = command->set_value.in_int;
            block.value.in_int = emulated_state.scn;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case SLP:
            if (command->action_type == WRITE) emulated_state.slp = command->set_value.in_int;
            block.value.in_int = emulated_state.slp;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case STA:
            if (command->action_type == WRITE) emulated_state.sta = command->set_value.in_int;
            block.value.in_int = emulated_state.sta;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case STC:
            if (command->action_type == WRITE) emulated_state.stc = command->set_value.in_int;
            block.value.in_int = emulated_state.stc;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case STE:
            if (command->action_type == WRITE) emulated_state.ste = command->set_value.in_int;
            block.value.in_int = emulated_state.ste;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case STS:
            if (command->action_type == WRITE) emulated_state.sts = command->set_value.in_int;
            block.value.in_int = emulated_state.sts;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case WCN:
            if (command->action_type == WRITE) emulated_state.wcn = command->set_value.in_int;
            block.value.in_int = emulated_state.wcn;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case WLP:
            if (command->action_type == WRITE) emulated_state.wlp = command->set_value.in_int;
            block.value.in_int = emulated_state.wlp;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;
        case WTC:
            if (command->action_type == WRITE) emulated_state.wtc = command->set_value.in_int;
            block.value.in_int = emulated_state.wtc;
            bsh_sys::mqtt_protocol::send_blocks(&block, 1, 0);
            break;

    default:
        break;
    }
}



/*===============================================*\
 * Local function definitions
\*===============================================*/
static void init_timer()
{
    const esp_timer_create_args_t timer_args = {telemetry_timer_handler, 0, {}, 0, 0};

	int ret = 0;
	if((ret = esp_timer_create(&timer_args, &telem_timer)) != ESP_OK) {
        ESP_LOGE(TAG,"cant create timer %d", ret);
        return;
    }	
}



static void telemetry_timer_handler(void *)
{
    send_entire_telemetry();
}



static void send_entire_telemetry()
{
    using namespace a630::mqtt_protocol;

    bsh_sys::mqtt_protocol::mqtt_out_block_t telem[ACTORS_QTY];     // !! >300 bytes on stack

    for (size_t i = 0; i < ACTORS_QTY; i++)
    {
        telem[i].index = 0;
        telem[i].capability = 0;
        telem[i].action_type = bsh_sys::mqtt_protocol::REPLY;
        telem[i].value_type = bsh_sys::mqtt_protocol::INT;

        memcpy (telem[i].actor, actor_code_to_string((actor_t)i), 3);

        switch (i)
        {
            case MOD: telem[i].value.in_int = emulated_state.mod; break;
            case STA: telem[i].value.in_int = emulated_state.sta; break;
            case ARA: telem[i].value.in_int = emulated_state.ara; break;
            case WLP: telem[i].value.in_int = emulated_state.wlp; break;
            case ALP: telem[i].value.in_int = emulated_state.alp; break;
            case STS: telem[i].value.in_int = emulated_state.sts; break;
            case STE: telem[i].value.in_int = emulated_state.ste; break;
            case SCN: telem[i].value.in_int = emulated_state.scn; break;
            case FSC: telem[i].value.in_int = emulated_state.fsc; break;
            case FSM: telem[i].value.in_int = emulated_state.fsm; break;
            case FST: telem[i].value.in_int = emulated_state.fst; break;
            case FSS: telem[i].value.in_int = emulated_state.fss; break;
            case FSR: telem[i].value.in_int = emulated_state.fsr; break;
            case WTC: telem[i].value.in_int = emulated_state.wtc; break;
            case STC: telem[i].value.in_int = emulated_state.stc; break;
            case WCN: telem[i].value.in_int = emulated_state.wcn; break;
            case CLR: telem[i].value.in_int = emulated_state.clr; break;
            case CLN: telem[i].value.in_int = emulated_state.cln; break;
            case SLP: telem[i].value.in_int = emulated_state.slp; break;

            case FFV:
            case FFU:
            case FFS:
            case BCT:
            case ACTORS_QTY:
            // not emulated
            break;
        }
    }
    bsh_sys::mqtt_protocol::send_blocks( telem, ACTORS_QTY, 0);
}



} // a630::emu namespace