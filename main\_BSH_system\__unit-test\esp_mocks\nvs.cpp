#include <string.h>
#include <stdlib.h>

#include "esp_err.h"
#include "nvs.h"

#define MAX_NVSS 5   // max nvs handles
#define MAX_PAIRS 20 // qty of key-value pairs in one nvs
#define MAX_KEY_LENGTH 20 

#define MAX_NVS_NAME_LENGTH 20

#define NOT_FOUND 0xFF

typedef enum {
    BLOB,
} data_type_t;


typedef struct {
    char key[MAX_KEY_LENGTH];
    data_type_t data_type;
    void* data_pointer;
    uint16_t data_size;   // for blob
} key_value_pair_t;

typedef struct {
    uint8_t nvs_n;
    char name[MAX_NVS_NAME_LENGTH];
} nvs_name_t;


class nvs_data
{
private:
    key_value_pair_t nvs_dta[MAX_NVSS][MAX_PAIRS];
    nvs_name_t nvs_names[MAX_NVSS];
public:
    nvs_data(/* args */);
    ~nvs_data();

    esp_err_t get_blob (uint8_t nvs_N, const char *key, void* out_value, size_t* out_length);
    esp_err_t set_blob (uint8_t nvs_N, const char *key, const void *data, uint16_t data_len);
    uint8_t get_name_number(const char *name);                 // returns NOT_FOUND if not found
    uint8_t get_key_position(uint8_t nvs_N, const char *key);  // returns NOT_FOUND if not found
    uint8_t add_nvs_name (const char *name);
    uint8_t get_next_free_key_position(uint8_t nvs_N);
    esp_err_t get_u16 (uint8_t nvs_N, const char* key, uint16_t* out_value);
    esp_err_t set_u16 (uint8_t nvs_N, const char* key, uint16_t value);
    esp_err_t get_u8 (uint8_t nvs_N, const char* key, uint8_t* out_value);
    esp_err_t set_u8 (uint8_t nvs_N, const char* key, uint8_t value);
    
    // mock tools
    void clean_data();
    void print_nvss();
    void print_data(uint8_t nvs_N);
};

nvs_data::nvs_data(/* args */)
{
    memset(nvs_dta, 0, sizeof(nvs_dta));
    memset(nvs_names, 0, sizeof(nvs_names));
}

nvs_data::~nvs_data()
{
    clean_data();
}

esp_err_t nvs_data::get_blob(uint8_t nvs_N, const char *key, void* out_value, size_t* out_length)
{
    uint8_t key_pos = get_key_position(nvs_N, key);
    if (key_pos == NOT_FOUND) return ESP_ERR_NVS_INVALID_NAME;

    memcpy(out_value, nvs_dta[nvs_N][key_pos].data_pointer, nvs_dta[nvs_N][key_pos].data_size);
    *out_length = nvs_dta[nvs_N][key_pos].data_size;

    return ESP_OK;
}

esp_err_t nvs_data::set_blob(uint8_t nvs_N, const char *key, const void *data, uint16_t data_len)
{
    uint8_t key_pos = get_key_position(nvs_N, key);

    if (key_pos == NOT_FOUND)
    {
        // create new
        key_pos = get_next_free_key_position(nvs_N);
        if (key_pos == NOT_FOUND) 
        {
            printf ("err  nvs mock has no free space");
            return ESP_ERR_NOT_FINISHED;
        }
        strcpy(nvs_dta[nvs_N][key_pos].key, key);
    } 
    
    nvs_dta[nvs_N][key_pos].data_type = BLOB;
    nvs_dta[nvs_N][key_pos].data_size = data_len;
    if (nvs_dta[nvs_N][key_pos].data_pointer != NULL) free (nvs_dta[nvs_N][key_pos].data_pointer);
    nvs_dta[nvs_N][key_pos].data_pointer = malloc(data_len);
    memcpy(nvs_dta[nvs_N][key_pos].data_pointer, data, data_len);

    return ESP_OK;
}



esp_err_t nvs_data::get_u16 (uint8_t nvs_N, const char* key, uint16_t* out_value)
{
    size_t length = 2;
    return get_blob(nvs_N, key, out_value, &length);
}

esp_err_t nvs_data::set_u16 (uint8_t nvs_N, const char* key, uint16_t value)
{
    return set_blob(nvs_N, key, &value, 2);
}



esp_err_t nvs_data::get_u8 (uint8_t nvs_N, const char* key, uint8_t* out_value)
{
    size_t length = 1;
    return get_blob(nvs_N, key, out_value, &length);
}

esp_err_t nvs_data::set_u8 (uint8_t nvs_N, const char* key, uint8_t value)
{
    return set_blob(nvs_N, key, &value, 1);
}



uint8_t nvs_data::get_name_number(const char *name)
{
    if (name == NULL) return NOT_FOUND;
    if (strlen(name) > MAX_NVS_NAME_LENGTH) return NOT_FOUND;

    for (size_t i = 0; i < MAX_NVSS; i++)
    {
        if (strcmp (name, nvs_names[i].name) == 0) return i;
    }
    return NOT_FOUND;
    
}

uint8_t nvs_data::get_key_position(uint8_t nvs_N, const char *key)
{
    if (key == NULL) return NOT_FOUND;
    if (strlen(key) > MAX_NVS_NAME_LENGTH) return NOT_FOUND;

    for (size_t i = 0; i < MAX_PAIRS; i++)
    {
        if (strcmp(nvs_dta[nvs_N][i].key, key) == 0) return i;
    }

    return NOT_FOUND;
}

uint8_t nvs_data::add_nvs_name(const char *name)
{
    if (name == NULL) return NOT_FOUND;
    if (strlen(name) > MAX_NVS_NAME_LENGTH) return NOT_FOUND;

    for (size_t i = 0; i < MAX_NVSS; i++)
    {
        // printf ("looking for name i: %i, name: %i \n",i, nvs_names[i].name[0]);
        if (nvs_names[i].name[0] == 0)
        {
            strcpy (nvs_names[i].name, name);
            return i;
        }
    }
    
    return NOT_FOUND;
}

uint8_t nvs_data::get_next_free_key_position(uint8_t nvs_N)
{
    for (size_t i = 0; i < MAX_PAIRS; i++)
    {
        if (nvs_dta[nvs_N][i].key[0] == 0)
        {
            return i;
        }
    }
    return NOT_FOUND;
}

void nvs_data::clean_data()
{
    for (size_t i = 0; i < MAX_NVSS; i++)
    {
        for (size_t j = 0; j < MAX_PAIRS; j++)
        {
            // if (nvs_dta[i][j].data_pointer != NULL) free(nvs_dta[i][j].data_pointer);
        }
    }
    
    memset(nvs_dta, 0, sizeof(nvs_dta));
    memset(nvs_names, 0, sizeof(nvs_names));
}

void nvs_data::print_nvss()
{
    printf("\n");
    for (size_t i = 0; i < MAX_NVSS; i++)
    {
        printf("id: %li   |  name: %s \n", i,  nvs_names[i].name);
    }
}

void nvs_data::print_data(uint8_t nvs_N)
{
    key_value_pair_t item;
    uint8_t no_data[5] = {};
    for (size_t i = 0; i < MAX_PAIRS; i++)
    {
        item = nvs_dta[nvs_N][i];
        uint8_t *pointer = (uint8_t *)item.data_pointer;
        if(pointer == NULL) pointer = no_data;
        printf("key: %s  |  data_size: %i  |  data type: %i | data %02x %02x %02x %02x %02x ...\n", 
            item.key, item.data_size, item.data_type, *pointer, *(pointer+1),*(pointer+2),*(pointer+3),*(pointer+4));
    }
}

/*************************************
 *      nvs interface functions
 ************************************/

static nvs_data *data = new(nvs_data);


esp_err_t nvs_get_blob(nvs_handle_t handle, const char* key, void* out_value, size_t* length)
{
    return data->get_blob((uint8_t)handle, key, out_value, length);
}



esp_err_t nvs_set_blob(nvs_handle_t handle, const char* key, const void* value, size_t length)
{
    return data->set_blob((uint8_t)handle, key, value, length);
}



esp_err_t nvs_get_u16 (nvs_handle_t handle, const char* key, uint16_t* out_value)
{
    return data->get_u16((uint8_t) handle, key, out_value);
}

esp_err_t nvs_set_u16 (nvs_handle_t handle, const char* key, uint16_t value)
{
    return data->set_u16((uint8_t) handle, key, value);
}



esp_err_t nvs_get_u8 (nvs_handle_t handle, const char* key, uint8_t* out_value)
{
    return data->get_u8((uint8_t) handle, key, out_value);
}

esp_err_t nvs_set_u8 (nvs_handle_t handle, const char* key, uint8_t value)
{
    return data->set_u8((uint8_t) handle, key, value);
}



esp_err_t nvs_open(const char *name, nvs_open_mode_t open_mode, nvs_handle_t *out_handle)
{
    if (strlen(name) == 0) return ESP_ERR_NVS_NOT_FOUND;
    if (open_mode == NVS_READONLY && data->get_name_number(name) == NOT_FOUND) return ESP_ERR_NVS_NOT_FOUND;

    uint8_t name_id = data->get_name_number(name);
    if ( name_id != NOT_FOUND)
    {
        *out_handle = name_id;
        return ESP_OK;
    }

    // create new name
    uint8_t res = data->add_nvs_name(name);
    if (res == NOT_FOUND) 
    {
        return ESP_ERR_INVALID_ARG;
    } 

    *out_handle = res;
    return ESP_OK;
}


esp_err_t nvs_commit(nvs_handle_t handle)
{
    return ESP_OK;
}


void nvs_close(nvs_handle_t handle)
{

}



void clean_data()
{
    data->clean_data();
}

void print_nvss()
{
    data->print_nvss();
}

void print_data(uint8_t nvs_N)
{
    data->print_data(nvs_N);
}