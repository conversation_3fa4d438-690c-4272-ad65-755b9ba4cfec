#include <stdint.h>
#include <string.h>

#include "esp_system.h"
#include "esp_log.h"
#include "esp_err.h"
#include "mqtt_client.h"
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "freertos/task.h"
#include "esp_timer.h"

#include "mqtt.h"
#include "reconnect_logic.h"



#define free_pointer_if_not_null(P) if (P != NULL) {free(P); P = NULL;}



namespace bsh_sys::mqtt {



ESP_EVENT_DEFINE_BASE(MQTT_EVENTS);



/* local objects declarations -----------------------------------------------*/
static const char *TAG = "-mqtt task-";
static esp_mqtt_client_handle_t client = NULL;
static mqtt_settings_t *mqtt_settings;
static connection_settings_t *broker_conn_sttgs = NULL;
static bool broker_connected = false;
static bool mqtt_client_initialized = false;
static TaskHandle_t sending_task_handle;
static char *incoming_buffer = NULL;
static QueueHandle_t out_queue;



/* local function declarations  ------------------------------------ */
// static esp_err_t mqtt_event_handler(esp_mqtt_event_handle_t event_data);
static void mqtt_event_handler(void* event_handler_arg,
                                        esp_event_base_t event_base,
                                        int32_t event_id,
                                        void* event_data);
static void sending_task(void *);
static void connect_to_broker();
static bool params_check(mqtt_settings_t *settings);
static bool conn_settings_check (connection_settings_t *settings);

static void CallWithDelay(void (*function)(void*),uint16_t delay);
static void stop_reconn(void *);
static const char * mqtt_return_code_to_string(int code);
static void reconnect_callback();



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
esp_err_t start(mqtt_settings_t *mqtt_stts)
{
    ESP_LOGI (TAG,"Starting mqtt...");
    if (!params_check(mqtt_stts)) return ESP_ERR_INVALID_ARG;
    mqtt_settings = mqtt_stts;

    out_queue = xQueueCreate(MQTT_OUT_QUEUE_SIZE, sizeof(bsh_sys::mqtt::mqtt_out_obj_t));

    esp_err_t ret;
    ret = xTaskCreate(sending_task, "mqttTask", 4096, NULL, 12, &sending_task_handle);
    if (ret != pdPASS) 
    {
        ESP_LOGE(TAG, "Failed to initialize mqtt task (%s), restarting device...", esp_err_to_name(ret));
        esp_restart();
    }

    reconnect_init(reconnect_callback);
    ESP_LOGI (TAG,"    done.");

    return ESP_OK;
}



QueueHandle_t get_sending_queue()
{
    return out_queue;
}


esp_err_t connect(connection_settings_t *conn_settings)
{
    if (!conn_settings_check(conn_settings)) return ESP_ERR_INVALID_ARG;
    broker_conn_sttgs = conn_settings;
    connect_to_broker();
    return ESP_OK;
}



bool is_broker_connected()
{
    return broker_connected;
}



void disconnect_from_broker()
{
    // if (!is_broker_connected()) return;
    stop_reconnecting();
    esp_mqtt_client_disconnect(client);
}



void stop()
{
    esp_mqtt_client_stop(client); 
    stop_reconnecting();
    free_pointer_if_not_null(incoming_buffer);
    vTaskDelete(sending_task_handle);
}



void stop_reconnect()
{
    if(!mqtt_client_initialized) return;
    ESP_LOGI (TAG,"stopped reconnect");
    CallWithDelay(stop_reconn, 1);
    // called with delay coz cant call stop from event handler
}



void put_obj_to_queue(mqtt_out_obj_t *obj)
{
    if (mqtt_settings == NULL)
    {
        ESP_LOGE (TAG, "module not initialized");
        return;
    }

    xQueueSend(out_queue, obj, 0);
}



void fake_incoming_pkt(uint8_t *data, uint16_t data_len)
{
    if (mqtt_settings == NULL)
    {
        ESP_LOGE (TAG, "module not initialized");
        return;
    }

    mqtt_settings->incoming_packet_treat(data, data_len);
}



int mqtt_post_string (char* string_to_post, uint16_t len, int QOS)
{
    printf("\n\n");
    ESP_LOGW(TAG, "====> to mqtt...");
    ESP_LOG_BUFFER_HEXDUMP(TAG, string_to_post, len, ESP_LOG_INFO);

    if (!mqtt_client_initialized)
    {
        ESP_LOGW(TAG, "    client not initialized");
        return 0;
    }

    if (!broker_connected)
    {
        ESP_LOGW(TAG,"    broker not connected");
        return 0;
    }

    int res;
    res = esp_mqtt_client_publish(client, broker_conn_sttgs->write_topic, string_to_post, len, QOS, 0);
    if (res == -1)
    {
        ESP_LOGW (TAG, "    can't post message");
        return res;
    }
    ESP_LOGI(TAG, "    sent %u bytes to mqtt", len);
    // esp_log_buffer_hex(TAG, string_to_post, len);
    return res;
}


int mqtt_post_string_to_topic(const char *topic, const char *string, int QOS)
{
    ESP_LOGW(TAG, "====> logs to mqtt...");

    if (!mqtt_client_initialized)
    {
        ESP_LOGE(TAG, "    client not initialized");
        return 0;
    }

    if (!broker_connected)
    {
        ESP_LOGE(TAG,"    broker not connected");
        return 0;
    }

    int res = esp_mqtt_client_publish(client, topic, string, strlen(string) > 500 ? 500 : strlen(string), QOS, 0);
    if (res == -1)
    {
        ESP_LOGW (TAG, "    can't post message");
        return res;
    }
    return res;
}



/*===============================================*\
 * Local function definitions
\*===============================================*/

/** 
 * Task waits for messages in incoming queue, then sends message to mqtt stack
 * sending api is actually a blocking api
 */
static void sending_task(void *)
{
    static mqtt_out_obj_t to_send;
    
    while (1)
    {
        if (xQueueReceive( out_queue, &to_send, portMAX_DELAY)) 
        {
            // ESP_LOGI (TAG,"got %u bytes of data from out queue", to_send.data_len);
            // ESP_LOG_BUFFER_HEXDUMP(TAG, &to_send, to_send.data_len, ESP_LOG_INFO);
            mqtt_post_string (to_send.out_data, to_send.data_len , to_send.qos_level);
            vTaskDelay (pdMS_TO_TICKS(10));
        }
    }
}



static void reconnect_callback()
{
    if(client != NULL) 
    {
        esp_mqtt_client_reconnect(client);
    } else {
        ESP_LOGE (TAG, "no client");
    }
}



static void connect_to_broker()
{
    esp_mqtt_client_config_t mqtt_cfg = {};
    mqtt_cfg.broker.address.uri = broker_conn_sttgs->broker_addr;
    mqtt_cfg.credentials.client_id = broker_conn_sttgs->device_guid; 
    mqtt_cfg.credentials.username = broker_conn_sttgs->dev_id_as_string; 
    mqtt_cfg.credentials.authentication.password = broker_conn_sttgs->user_passw; 
    mqtt_cfg.network.disable_auto_reconnect = true;

    ESP_LOGI (TAG,"connecting to broker. client_id: %s  uname: %s psw: %s ",
                    mqtt_cfg.credentials.client_id,
                    mqtt_cfg.credentials.username,
                    mqtt_cfg.credentials.authentication.password);
    if (!mqtt_client_initialized)
    {
        client = esp_mqtt_client_init(&mqtt_cfg);
        mqtt_client_initialized = true;

        esp_mqtt_client_register_event(client, MQTT_EVENT_ANY, mqtt_event_handler, NULL);

        esp_mqtt_client_start(client);
    } else {
        // esp_mqtt_client_stop(client);
        // esp_mqtt_client_disconnect(client);
        ESP_ERROR_CHECK(esp_mqtt_set_config(client, &mqtt_cfg));
        esp_mqtt_client_reconnect(client);
        // ESP_ERROR_CHECK(esp_mqtt_client_reconnect(client));
    }
}


/*
 * @brief Event handler registered to receive MQTT events
 *
 *  This function is called by the MQTT client event loop.
 *
 * @param handler_args user data registered to the event.
 * @param base Event base for the handler(always MQTT Base in this example).
 * @param event_id The id for the received event.
 * @param event_data The data for the event, esp_mqtt_event_handle_t.
 */
static void mqtt_event_handler(void* event_handler_arg,
                                        esp_event_base_t event_base,
                                        int32_t event_id,
                                        void* event_data)
// static esp_err_t mqtt_event_handler(esp_mqtt_event_handle_t event_data)
{
    esp_mqtt_event_handle_t event = (esp_mqtt_event_handle_t)event_data;
    esp_mqtt_client_handle_t client = event->client;
    int msg_id;
    
    switch (event->event_id) {
    case MQTT_EVENT_CONNECTED:
        ESP_LOGI(TAG, "         CONNECTED TO BROKER");
        broker_connected = true;
        esp_event_post_to(mqtt_settings->loop_handle, MQTT_EVENTS, MQTT_BROKER_CONNECTED, NULL, 0, 0);
        msg_id = esp_mqtt_client_subscribe(client, broker_conn_sttgs->read_topic, 0);
        ESP_LOGI(TAG, "sent subscribe request (-1 means error), msg_id=%d", msg_id);
        break;

    case MQTT_EVENT_DISCONNECTED:
        ESP_LOGI(TAG, "disconnected");
        if(broker_connected) esp_mqtt_client_reconnect(client);
        broker_connected = false;
        esp_event_post_to(mqtt_settings->loop_handle, MQTT_EVENTS, MQTT_BROKER_DISCONNECTED, NULL, 0, 0);
        break;

    case MQTT_EVENT_SUBSCRIBED:
        ESP_LOGI(TAG, "subscribed, msg_id=%d", event->msg_id);
        esp_event_post_to(mqtt_settings->loop_handle, MQTT_EVENTS, MQTT_BROKER_SUBSCRIBED, NULL, 0, 0);
        break;

    case MQTT_EVENT_UNSUBSCRIBED:
        ESP_LOGI(TAG, "unsubscribed, msg_id=%d", event->msg_id);
        break;

    case MQTT_EVENT_PUBLISHED:
        ESP_LOGI(TAG, "MQTT_EVENT_PUBLISHED, msg_id=%d", event->msg_id);
        esp_event_post_to(mqtt_settings->loop_handle, MQTT_EVENTS, MQTT_BROKER_MSG_PUBLISHED, NULL, 0, 0);
        bsh_sys::mqtt::connect_result(MQTT_CONNECTION_ACCEPTED);
        break;

    case MQTT_EVENT_DATA:
        printf("\n\n");
        ESP_LOGI(TAG, "<=== mqtt command:");
            if (event->data_len < 500 ) {
                ESP_LOG_BUFFER_HEXDUMP(TAG, event->data, event->data_len, ESP_LOG_INFO);
            } else {
                ESP_LOGW(TAG, "too long data: %u", event->data_len);
            }

        mqtt_settings->incoming_packet_treat((uint8_t *)event->data, event->data_len);        
        break;

    case MQTT_EVENT_ERROR:

        ESP_LOGW(TAG, "mqtt event ERR TYPE: %i    (1= transpot, 2= creds)", event->error_handle->error_type);
        if(event->error_handle->error_type == MQTT_ERROR_TYPE_CONNECTION_REFUSED)
        {
            ESP_LOGW(TAG, "mqtt event ERR CODE: %s", mqtt_return_code_to_string(event->error_handle->connect_return_code));
        }
        
        switch (event->error_handle->error_type)
        {
        case MQTT_ERROR_TYPE_TCP_TRANSPORT:
            esp_event_post_to(mqtt_settings->loop_handle, MQTT_EVENTS, MQTT_BROKER_TRANPORT_ERR, NULL, 0, 0);
            bsh_sys::mqtt::connect_result(MQTT_CONNECTION_REFUSE_SERVER_UNAVAILABLE);
            break;
        case MQTT_ERROR_TYPE_CONNECTION_REFUSED:
            ESP_LOGW(TAG, "connection refused: %s", mqtt_return_code_to_string(event->error_handle->connect_return_code));
            esp_event_post_to(mqtt_settings->loop_handle, MQTT_EVENTS, event->error_handle->connect_return_code, NULL, 0, 0);
            bsh_sys::mqtt::connect_result(event->error_handle->connect_return_code);
            break;
        default:
            ESP_LOGW(TAG, "unknown connection err");
            bsh_sys::mqtt::connect_result(MQTT_CONNECTION_REFUSE_SERVER_UNAVAILABLE);
            esp_event_post_to(mqtt_settings->loop_handle, MQTT_EVENTS, MQTT_BROKER_ERR, (void *)event->error_handle->error_type, 0, 0);
            break;
        }
        broker_connected = false;
        break;
    default:
        ESP_LOGI(TAG, "Other event id:%d", event->event_id);
        break;
    }
}



static bool params_check(mqtt_settings_t *settings)
{
    if (settings->incoming_packet_treat == NULL) 
    {
        ESP_LOGE (TAG,"NULL instead packet checking function ptr");
        return false;
    }

    return true;
}



static bool conn_settings_check (connection_settings_t *settings)
{
    if (settings->broker_addr == NULL)
    {
        ESP_LOGE (TAG,"no broker addr");
        return false;
    }

    if (settings->device_guid == NULL)
    {
        ESP_LOGE (TAG,"no device guid");
        return false;
    }

    if (settings->read_topic == NULL)
    {
        ESP_LOGE (TAG,"no read topic");
        return false;
    }

    if (settings->write_topic == NULL)
    {
        ESP_LOGE (TAG,"no write topic");
        return false;
    }
    
    if (settings->user_id == NULL || settings->user_passw == NULL)
    {
        ESP_LOGE (TAG,"no user_if / passw");
        return false;
    }

    return true;
}



static void stop_reconn(void *)
{
    stop_reconnecting();
    esp_mqtt_client_disconnect(client);
}



/*
 *  Delayed function call
 */
static esp_timer_handle_t delayedCallTimer = NULL;
static void CallWithDelayCancel()
{
    if (delayedCallTimer == NULL) return;
    esp_timer_stop(delayedCallTimer);
    esp_timer_delete(delayedCallTimer);
    delayedCallTimer = NULL;
}

// delay is in seconds
static void CallWithDelay(void (*function)(void*),uint16_t delay)
{
    CallWithDelayCancel();

    const esp_timer_create_args_t DelayedTimerArgs = {function, 0, {}, 0, 0};
    ESP_ERROR_CHECK(esp_timer_create(&DelayedTimerArgs, &delayedCallTimer));

    esp_timer_start_once(delayedCallTimer, ((uint64_t)delay)*1000000);
}



static const char * mqtt_return_code_to_string(int code)
{
    switch (code)
    {
    case MQTT_CONNECTION_ACCEPTED:                       return "Connection accepted";
    case MQTT_CONNECTION_REFUSE_PROTOCOL:                return "Wrong protocol";
    case MQTT_CONNECTION_REFUSE_ID_REJECTED:             return "ID rejected";
    case MQTT_CONNECTION_REFUSE_SERVER_UNAVAILABLE:      return "Server unavailable";
    case MQTT_CONNECTION_REFUSE_BAD_USERNAME:            return "Wrong user";
    case MQTT_CONNECTION_REFUSE_NOT_AUTHORIZED:          return "Wrong username or password";
    default:                                             return "unknown";
    }
    return "unknown";
}



}  // namespace bsh_sys::mqtt