#include "CppUTest/TestHarness.h"
#include "esp_random.h"
#include "string.h"

extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}



TEST_GROUP(random)
{
    void setup()
    {
    }

    void teardown()
    {
    }
};

TEST(random, generates_random)
{
    uint8_t buff [10] = {};

    esp_fill_random(buff, sizeof(buff));

    for (size_t i = 0; i < sizeof(buff) - 2; i++)
    {
        if (buff[i] == 0 && buff[i+1] == 0 && buff[i+2] == 0) FAIL(" ");
    }

    // printf ("%i %i %i %i %i %i %i %i %i %i", buff [0], buff [1], buff [2], buff [3], buff [4], buff [5], buff [6], buff [7], buff [8], buff [9]);
}

TEST(random, set_next_random_works_ok)
{
    uint8_t buff[10] = {1,2,3,4,5,6,7,8,9,0};

    set_next_random_to(buff);

    uint8_t out_buff [10] = {};
    esp_fill_random(out_buff, 10);

    MEMCMP_EQUAL(buff, out_buff, 10);

    // next random is really random
    esp_fill_random(out_buff, 10);
    CHECK(memcmp(buff, out_buff, 10) != 0);
}
