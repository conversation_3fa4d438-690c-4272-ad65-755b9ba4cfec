#include "esp_err.h"
#include "task.h"

BaseType_t xTaskCreate(TaskFunction_t pvTaskCode, const char *const pcName, const uint32_t usStackDepth, void *const pvParameters, UBaseType_t uxPriority, TaskHandle_t *const pxCreatedTask)
{
    return 1;
}




void vTaskSuspendAll( void )
{

}



void xTaskResumeAll ( void )
{

}


void vTaskDelete(TaskHandle_t task)
{

}


void vTaskDelay( const TickType_t xTicksToDelay )
{
    
}