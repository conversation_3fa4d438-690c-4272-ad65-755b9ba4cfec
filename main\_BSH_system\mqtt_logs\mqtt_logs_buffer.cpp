#include <string.h>
#include <stdint.h>

#include "esp_log.h"
#include "esp_system.h"
#include "esp_timer.h"

#include "mqtt_logs_buffer.h"



/* ======= local typedef declarations =======*/
/* ======= local defines for constants =========*/
#define MAX_MSG_SIZE 250   // buffer for getting message function



namespace bsh_sys::mqtt_logs::logs_buffer {



static const char *TAG = "-mqtt logs buff-";


/* ======= local object declarations =========*/
static uint8_t *buffer = NULL;
static int buffer_size = 0;
static uint8_t *first_message = NULL;
static uint8_t *last_message = NULL;

static char get_msg_buff[MAX_MSG_SIZE] = {};
static uint8_t *buffer_end = NULL;




/* ======= local function declarations ========= */
static uint8_t *get_free_slot_of_size(uint16_t msg_size);
static void write_last_message(uint8_t *where, const char *msg, int msg_size);
static void remove_first_message();



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
int init_buffer(int size)
{
    if (buffer == NULL) free(buffer);

    buffer = (uint8_t *)malloc(size);
    if(buffer == NULL) { ESP_LOGE(TAG,"cant allocate log buffer"); esp_restart(); }
    buffer_size = size;
    buffer_end = buffer + buffer_size;
    first_message = NULL;
    last_message = NULL;
    return 0;
}



void deinit_buffer()
{
    free(buffer);
    buffer_size = 0;
    first_message = NULL;
    last_message = NULL;
    buffer_end = NULL;
}



int push_message(const char *message)
{
    // TODO implement compression?

    static uint8_t cycles;
    cycles = 0;
    int msg_size = strlen(message);
    if (msg_size > MAX_MSG_SIZE) 
    {
        ESP_LOGW(TAG,"long msg. truncated");
        msg_size = MAX_MSG_SIZE -1;  // -1 to att '\0' at the end
    };
    
    write_last_message(get_free_slot_of_size(msg_size  + 1 + 2), message, msg_size);
    return 0;
}



/**
 *  Message format:
 *  msg_size[2 bytes]log_level[1 byte]time[8bytes*2]||tag||message  hexbuffer\0
*/
int push_formatted_message(uint8_t log_level, const char *module_tag, const char *message, uint8_t *hex_dump, uint16_t dump_size, va_list argp)
{
    char n_buff[2];
    int formatted_msg_size = vsnprintf(n_buff, 1, message, argp);
    int msg_size = 2 + 1 + 16 + 2 + strlen(module_tag) + 2 + formatted_msg_size + (hex_dump != NULL ? dump_size*2 : 0) + 1;
    if(msg_size > MAX_MSG_SIZE) 
    {
        ESP_LOGW(TAG,"too long log message");
        return -1;
    }

    char *slot = (char *)get_free_slot_of_size(msg_size);

    last_message = (uint8_t *)slot;
    *last_message = (msg_size)>>8;
    *(last_message+1) = (msg_size)%0x100;
    *(last_message + msg_size) = '\0';
    if (first_message == NULL) first_message = last_message;
    
    slot+= 2; // 2 bytes contains msg length

    // put log level
    slot[0] = log_level;
    slot++;

    // put time
    snprintf(slot, 17, "%016lli", esp_timer_get_time()); // writes 8 symbols of system time. which is replaced by formatted time when data being pulled from buffer
    slot[16] = '|';
    slot[17] = '|';
    slot += 16 + 2;

    // put tag
    int tag_size = strlen(module_tag);
    memcpy(slot, module_tag, tag_size);
    slot[tag_size] = '|';
    slot[tag_size+1] = '|';
    slot += tag_size + 2;

    // put message
    slot += vsnprintf(slot, formatted_msg_size + 1, message, argp);

    // put buffer
    if(hex_dump != NULL && dump_size != 0)
    {
        slot[0] = ' ';
        slot[1] = ' ';
        slot += 2;

        for (size_t i = 0; i < dump_size ; i++)
        {
            snprintf(slot, MAX_MSG_SIZE, "%02x", hex_dump[i]);
            slot += 2;
        }
        slot[0] = '\0';
    }

    // string terminator
    // slot+= 2;
    // slot[0] = '\0';
    
    return 0;
}



char * get_message()
{
    if (first_message == NULL) return NULL;

    memcpy (get_msg_buff, first_message + 2, *first_message*0x100 + *(first_message + 1));
    remove_first_message();

    return get_msg_buff;
}



bool has_message()
{
    return last_message != NULL;
}



int get_buffer_percentage()
{
    // TODO
    return 0;
}




/*===============================================*\
 * Local function definitions
\*===============================================*/

static uint8_t *get_free_slot_of_size(uint16_t msg_size)
{
    int cycles = 0;
    // buffer is free
    start:
    if (last_message == NULL)
    {
        return buffer;
        // write_last_message(buffer, message, msg_size);
        // first_message = last_message;
    } else {  
        uint8_t *last_msg_end = last_message + 2 + *last_message*0x100 + *(last_message+1);
        if (last_message >= first_message) { //  state: [__12345_],
            
            if(buffer_end - last_msg_end >= msg_size)                  // message fits at the end.  
            {
                // write_last_message(last_msg_end, message, msg_size);
                return last_msg_end;
            } else if(first_message - buffer >= msg_size) {     // message fits at the front
                memset(last_msg_end, 0, buffer_end - last_msg_end);   // this unoccupied gap shall be filled with 0, indicating no data in there
                // write_last_message(buffer, message, msg_size);
                return buffer;
            } else {                                                    // message doesn't fit
                remove_first_message();
                cycles++;
                if(cycles > 30) {ESP_LOGE(TAG,"buffer err. clearing"); first_message = NULL; last_message = NULL; return 0;}
                goto start;
            }
        } else if (last_message < first_message) {// state [45__123]
            if((int)(first_message - last_msg_end) >= msg_size)        // msg can fit in free space
            {
                // write_last_message(last_msg_end, message, msg_size);
                return last_msg_end;
            } else {                                                    // msg can not fit
                remove_first_message();
                cycles++;
                if(cycles > 5) {ESP_LOGE(TAG,"buffer err. clearing"); first_message = NULL; last_message = NULL; return 0;}
                goto start;
            }
        }
    }
    return NULL;
}
static void write_last_message(uint8_t *where, const char *msg, int msg_size)
{
    last_message = where;
    *last_message = (msg_size + 1) >> 8;
    *(last_message + 1) = (msg_size + 1) % 0x100;
    memcpy(last_message + 2, msg, msg_size);
    *(last_message + 2 + msg_size) = '\0';

    if (first_message == NULL) first_message = last_message;
}



static void remove_first_message()
{
    if(first_message == NULL || last_message == NULL) 
    {
        ESP_LOGE(TAG,"err removing msg");
        return;
    }
    if (first_message == last_message) 
    { 
        first_message = NULL;
        last_message = NULL;
    } else {
        first_message = first_message + 2 + (*first_message*0x100 + *(first_message+1));
        if(*first_message == 0 && *(first_message+1) == 0) first_message = buffer; // the spot is empty space at the end of buffer, but there is msg in the front of it
    }
}


}