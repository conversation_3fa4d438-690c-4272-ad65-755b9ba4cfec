# BSH System CMakeLists.txt
# This file contains the build configuration for the BSH System submodule
cmake_minimum_required(VERSION 3.5)

# Define BSH system core source files (excluding OTA and WIFI submodules)
set(BSH_SYSTEM_SRCS
    "system.cpp"
    "mqtt_helpers.cpp"
    "mqtt_logs/mqtt_logs.cpp"
    "mqtt_logs/mqtt_logs_buffer.cpp"
    "BLE/bluetooth_LE.cpp"
    "BLE/ble_up_time.cpp"
    "BLE/first_start.cpp"
    "ble_comm/ble_comm_logic.cpp"
    "MQTT/mqtt.cpp"
    "MQTT/reconnect_logic.cpp"
    "mqtt_sys_protocol/mqtt_sys_protocol.cpp"
    "date_and_time/date_and_time.cpp"
    "request_to_balancer/http_request_to_balancer.cpp"
    "RSA_encryption/rsa_encryption.cpp"
    "UART/uart.cpp"
    "device_id/device_id.cpp"
    "commissioning/commissioning.cpp"
    "telemetry_storage/telemetry_storage.cpp"
)

# Define BSH system core include directories (excluding OTA and WIFI submodules)
set(BSH_SYSTEM_INCLUDE_DIRS
    "."
    "BLE"
    "ble_comm"
    "MQTT"
    "mqtt_sys_protocol"
    "request_to_balancer"
    "RSA_encryption"
    "UART"
    "date_and_time"
    "device_id"
    "commissioning"
    "telemetry_storage"
    "mqtt_logs"
    # Include OTA and WIFI headers for BSH system access
    "_OTA"
    "_WIFI"
)

# Define embedded text files
set(BSH_SYSTEM_EMBED_TXTFILES
    "RSA_encryption/esp32_rsa_priv_pair"
    "RSA_encryption/esp32_rsa_pub_pair"
)

# Include OTA and WIFI submodule configurations
include(_OTA/sources.cmake)
include(_WIFI/sources.cmake)

# Note: All source files, include directories, and embedded files are now defined in sources.cmake
# This CMakeLists.txt serves as documentation of the BSH system structure and submodule inclusion
