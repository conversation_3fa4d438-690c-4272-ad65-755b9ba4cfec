test-obj/mocks_tests/nvs_tests.o: mocks_tests/nvs_tests.cpp \
 /opt/cpputest/include/CppUTest/MemoryLeakDetectorNewMacros.h \
 /opt/cpputest/include/CppUTest/CppUTestConfig.h \
 /opt/cpputest/include/CppUTest/CppUTestGeneratedConfig.h \
 /opt/cpputest/include/CppUTest/StandardCLibrary.h \
 /opt/cpputest/include/CppUTest/MemoryLeakDetectorMallocMacros.h \
 /opt/cpputest/include/CppUTest/TestHarness.h \
 /opt/cpputest/include/CppUTest/Utest.h \
 /opt/cpputest/include/CppUTest/SimpleString.h \
 /opt/cpputest/include/CppUTest/UtestMacros.h \
 /opt/cpputest/include/CppUTest/TestResult.h \
 /opt/cpputest/include/CppUTest/TestFailure.h \
 /opt/cpputest/include/CppUTest/TestPlugin.h \
 /opt/cpputest/include/CppUTest/MemoryLeakWarningPlugin.h \
 /opt/cpputest/include/CppUTest/MemoryLeakDetectorNewMacros.h nvs.h \
 esp_err.h
/opt/cpputest/include/CppUTest/MemoryLeakDetectorNewMacros.h:
/opt/cpputest/include/CppUTest/CppUTestConfig.h:
/opt/cpputest/include/CppUTest/CppUTestGeneratedConfig.h:
/opt/cpputest/include/CppUTest/StandardCLibrary.h:
/opt/cpputest/include/CppUTest/MemoryLeakDetectorMallocMacros.h:
/opt/cpputest/include/CppUTest/TestHarness.h:
/opt/cpputest/include/CppUTest/Utest.h:
/opt/cpputest/include/CppUTest/SimpleString.h:
/opt/cpputest/include/CppUTest/UtestMacros.h:
/opt/cpputest/include/CppUTest/TestResult.h:
/opt/cpputest/include/CppUTest/TestFailure.h:
/opt/cpputest/include/CppUTest/TestPlugin.h:
/opt/cpputest/include/CppUTest/MemoryLeakWarningPlugin.h:
/opt/cpputest/include/CppUTest/MemoryLeakDetectorNewMacros.h:
nvs.h:
esp_err.h:
