# The following lines of boilerplate have to be in your project's CMakeLists
# in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.5)


set(srcs
    "main.cpp"
)

# Include the BSH system submodule to get source lists
add_subdirectory(_BSH_system)

# Collect all source files (main project + BSH system)
set(ALL_SRCS
    ${srcs}
    "emulator/emulator.cpp"
    "main_task/main_task.cpp"
    "main_task/queues_helpers.cpp"
    "uart_protocol/uart_protocol.cpp"
    "mqtt_protocol/mqtt_protocol.cpp"
    "telemetry_filter/telemetry_filter.cpp"
)

# Add BSH system sources with proper path prefixes
foreach(BSH_SRC ${BSH_ALL_SRCS})
    list(APPEND ALL_SRCS "_BSH_system/${BSH_SRC}")
endforeach()

# Collect all include directories
set(ALL_INCLUDE_DIRS
    "."
    "./emulator"
    "./main_task"
    "./uart_protocol"
    "./mqtt_protocol"
    "./telemetry_filter"
    "./_BSH_system"
)

# Add BSH system include directories with proper path prefixes
foreach(BSH_INC ${BSH_SYSTEM_INCLUDE_DIRS})
    list(APPEND ALL_INCLUDE_DIRS "./_BSH_system/${BSH_INC}")
endforeach()

# Add BSH system embedded files with proper path prefixes
set(ALL_EMBED_TXTFILES)
foreach(BSH_EMBED ${BSH_SYSTEM_EMBED_TXTFILES})
    list(APPEND ALL_EMBED_TXTFILES "_BSH_system/${BSH_EMBED}")
endforeach()

# Register the complete component (main project + BSH system)
idf_component_register(
    SRCS ${ALL_SRCS}
    INCLUDE_DIRS ${ALL_INCLUDE_DIRS}
    EMBED_TXTFILES ${ALL_EMBED_TXTFILES}
)


