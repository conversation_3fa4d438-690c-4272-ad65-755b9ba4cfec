# The following lines of boilerplate have to be in your project's CMakeLists
# in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.5)


set(srcs
    "main.cpp"
)

idf_component_register(SRCS "${srcs}" 
                        "emulator/emulator.cpp"
                        "main_task/main_task.cpp"
                        "main_task/queues_helpers.cpp"
                        "uart_protocol/uart_protocol.cpp"
                        "mqtt_protocol/mqtt_protocol.cpp"
                        "telemetry_filter/telemetry_filter.cpp"


                        "_BSH_system/system.cpp"
                        "_BSH_system/mqtt_helpers.cpp"
                        "_BSH_system/mqtt_logs/mqtt_logs.cpp"
                        "_BSH_system/mqtt_logs/mqtt_logs_buffer.cpp"

                        "_BSH_system/BLE/bluetooth_LE.cpp"
                        "_BSH_system/BLE/ble_up_time.cpp"
                        "_BSH_system/BLE/first_start.cpp"
                        
                        "_BSH_system/ble_comm/ble_comm_logic.cpp"
                        "_BSH_system/MQTT/mqtt.cpp"
                        "_BSH_system/MQTT/reconnect_logic.cpp"
                        "_BSH_system/mqtt_sys_protocol/mqtt_sys_protocol.cpp"
                        "_BSH_system/date_and_time/date_and_time.cpp"
                        "_BSH_system/request_to_balancer/http_request_to_balancer.cpp"
                        "_BSH_system/RSA_encryption/rsa_encryption.cpp"
                        "_BSH_system/UART/uart.cpp"
                        "_BSH_system/_WIFI/wifi.cpp"
                        "_BSH_system/device_id/device_id.cpp"
                        "_BSH_system/commissioning/commissioning.cpp"
                        "_BSH_system/telemetry_storage/telemetry_storage.cpp"

                        "_BSH_system/_OTA/bsh_ota.cpp"

                        INCLUDE_DIRS 
                        "." 
                        "./emulator" 
                        "./main_task"  
                        "./uart_protocol"
                        "./mqtt_protocol"
                        "./telemetry_filter"

                        "./_BSH_system" 
                        "./_BSH_system/BLE" 
                        "./_BSH_system/ble_comm/"
                        "./_BSH_system/MQTT" 
                        "./_BSH_system/mqtt_sys_protocol" 
                        "./_BSH_system/request_to_balancer"
                        "./_BSH_system/RSA_encryption"
                        "./_BSH_system/UART"
                        "./_BSH_system/_WIFI"
                        "./_BSH_system/date_and_time"
                        "./_BSH_system/device_id"
                        "./_BSH_system/commissioning"
                        "./_BSH_system/telemetry_storage"
                        "./_BSH_system/mqtt_logs"

                        "./_BSH_system/_OTA"
                        
                     EMBED_TXTFILES _BSH_system/RSA_encryption/esp32_rsa_priv_pair
                     EMBED_TXTFILES _BSH_system/RSA_encryption/esp32_rsa_pub_pair)


