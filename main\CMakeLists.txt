# The following lines of boilerplate have to be in your project's CMakeLists
# in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.5)


set(srcs
    "main.cpp"
)

# Include BSH system configuration
include(_BSH_system/sources.cmake)

# Register the complete component (main project + BSH system)
idf_component_register(SRCS "${srcs}"
                        "emulator/emulator.cpp"
                        "main_task/main_task.cpp"
                        "main_task/queues_helpers.cpp"
                        "uart_protocol/uart_protocol.cpp"
                        "mqtt_protocol/mqtt_protocol.cpp"
                        "telemetry_filter/telemetry_filter.cpp"
                        ${BSH_ALL_SRCS_WITH_PATH}

                        INCLUDE_DIRS
                        "."
                        "./emulator"
                        "./main_task"
                        "./uart_protocol"
                        "./mqtt_protocol"
                        "./telemetry_filter"
                        ${BSH_ALL_INCLUDE_DIRS_WITH_PATH}

                     EMBED_TXTFILES ${BSH_ALL_EMBED_FILES_WITH_PATH})


