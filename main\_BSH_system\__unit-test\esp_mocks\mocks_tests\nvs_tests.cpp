#include "CppUTest/TestHarness.h"
#include "nvs.h"


extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}

TEST_GROUP(NVS_open)
{
    void setup()
    {
    }

    void teardown()
    {
        clean_data();  // if no cleanup - memory leak detected
    }
};

TEST(NVS_open, returns_some_handle)
{
    clean_data();
    nvs_handle_t handle = 0xFF;
    esp_err_t res = nvs_open("1", NVS_READWRITE, &handle);
    CHECK(res == ESP_OK);
    CHECK(handle == 0);

    res = nvs_open("2", NVS_READWRITE, &handle);
    CHECK(res == ESP_OK);
    CHECK(handle == 1);
}

TEST(NVS_open, read_only_open_of_non_existing_name_is_err)
{
    clean_data();
    nvs_handle_t handle = 0xFF;
    esp_err_t res = nvs_open("3", NVS_READONLY, &handle);
    CHECK(res == ESP_ERR_NVS_NOT_FOUND);
    CHECK(handle == 0xFF);
}

TEST(NVS_open, empty_name_err)
{
    clean_data();
    nvs_handle_t handle = 0xFF;
    esp_err_t res = nvs_open("", NVS_READWRITE, &handle);
    CHECK(res == ESP_ERR_NVS_NOT_FOUND);
    CHECK(handle == 0xFF);
}

TEST(NVS_open, check_no_overwrite_of_existing_name)
{
    clean_data();
    nvs_handle_t handle = 0xFF;
    nvs_open("123", NVS_READWRITE, &handle);
    CHECK(handle == 0);
    nvs_open("456", NVS_READWRITE, &handle);
    CHECK(handle == 1);
    nvs_open("456", NVS_READWRITE, &handle);
    CHECK(handle == 1);
}



TEST_GROUP(NVS_set_blob_get_blob)
{
    void setup()
    {
        MemoryLeakWarningPlugin::saveAndDisableNewDeleteOverloads();
    }

    void teardown()
    {
        clean_data();  // if no cleanup - memory leak detected
        MemoryLeakWarningPlugin::restoreNewDeleteOverloads();
    }
};



TEST(NVS_set_blob_get_blob, ok_case)
{
    uint8_t blob[10] = {1,2,3,4,5,6,7,8,9,10};
    uint8_t out_blob[10] = {};
    
    clean_data();

    nvs_handle_t handle;
    esp_err_t res = nvs_open("123", NVS_READWRITE, &handle);
    res = nvs_set_blob(handle, "key", blob, sizeof(blob));

    // print_nvss();
    // printf("\n");
    // print_data(handle);

    CHECK(res == ESP_OK);

    size_t out_size = 0;
    nvs_get_blob(handle, "key", out_blob, &out_size);
    
    MEMCMP_EQUAL(blob, out_blob, 10);
    CHECK(out_size == 10);
}



TEST_GROUP(NVS_set_u16_get_u16)
{
    void setup()
    {
        MemoryLeakWarningPlugin::saveAndDisableNewDeleteOverloads();
    }

    void teardown()
    {
        clean_data();  // if no cleanup - memory leak detected
        MemoryLeakWarningPlugin::restoreNewDeleteOverloads();
    }
};

TEST(NVS_set_u16_get_u16, ok_case)
{
    uint16_t value_to_set = 8;

    clean_data();

    nvs_handle_t handle;
    nvs_open("123", NVS_READWRITE, &handle);
    nvs_set_u16(handle, "key", value_to_set);

    uint16_t value_got_back = 0;
    nvs_get_u16(handle, "key", &value_got_back);

    CHECK (value_to_set == value_got_back);

    nvs_set_u8(handle, "key2", value_to_set);
    uint8_t value_got_back2 = 0;
    nvs_get_u8(handle, "key2", &value_got_back2);

    clean_data();
}



