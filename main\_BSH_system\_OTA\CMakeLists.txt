# OTA Submodule CMakeLists.txt
# This file contains the build configuration for the OTA submodule
# Note: This submodule's sources are included in the parent BSH system component

cmake_minimum_required(VERSION 3.5)

# Define OTA source files (for documentation and potential future use)
set(OTA_SRCS
    "bsh_ota.cpp"
)

# Define OTA include directories (for documentation and potential future use)
set(OTA_INCLUDE_DIRS
    "."
)

# Note: The actual compilation is handled by the main/CMakeLists.txt
# Source definitions are in sources.cmake
# This file serves as documentation of the OTA submodule structure
