#pragma once

#include "esp_event.h"
#include "esp_wifi.h"



#define MAXIMUM_RETRY_FOR_ONE_ATTEMPT  3
#define RECONNECT_TIME 15  // s    
#define RECONNECT_TIME_SHORT 3 //s
#define FAST_RECONNECT_TRIES  20 



namespace bsh_sys::wifi {



ESP_EVENT_DECLARE_BASE(WIFI_EVENTS);



typedef enum {
    WIFI_CONNECTED,
    WIFI_DISCONNECTED,
    WIFI_CONN_ERR_NETWORK_NOT_FOUND,
    WIFI_CONN_ERR_WRONG_PASSW,
    WIFI_CONN_ERR_NO_IP,
    WIFI_STATUS_QTY,
} wifi_events_t;


typedef struct 
{
    char *ssid;
    char *passw;
    esp_event_loop_handle_t loop_handle;
} bsh_wifi_settings_t;



/**
 * @brief Initializes wifi, and connects
 */  
esp_err_t start_and_connect(bsh_wifi_settings_t *settings);

/**
 * @brief stops sending wifi events till next connect command
 */  
void stop_reconnecting();

bool is_connected();
void send_rssi_regulary (void(* rssi_cb)(int8_t rssi), uint16_t period_seconds);
int8_t get_rssi();



void wifi_set_power_saving_mode(wifi_ps_type_t type);



} // bsh_sys::wifi namespace