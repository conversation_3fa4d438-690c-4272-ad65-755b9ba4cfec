#include <string.h>
#include <math.h>

#include "esp_timer.h"
#include "esp_log.h"

#include "telemetry_filter.h"
#include "mqtt_protocol.h"
#include "system.h"


namespace a630::tlm_filter {



#define PUSH_TELEM_DELAY 800    //ms



/* ======= local defines for constants =========*/
static const char *TAG = "-T filter-";



/* ======= local object declarations =========*/
static char fun_prod_id[25] = {};



/* ======= local function declarations ========= */
static bool is_critical_dp_changed(bsh_sys::mqtt_protocol::mqtt_out_block_t *old_blk, 
                    bsh_sys::mqtt_protocol::mqtt_out_block_t *new_blk);
static bool dp_to_mqtt_block(a630::uart_protocol::tuya_data_point_t *dp, bsh_sys::mqtt_protocol::mqtt_out_block_t *mqtt_out_block);
static uint32_t from_big_endian (uint8_t *data, uint8_t len);

// dps data to in conversions (coz there is no string in our mqtt protocol)
static int fungene_color_to_int (uint8_t *data, uint8_t len);
static int fast_smell_repeat_to_int (uint8_t *data, uint8_t len);




/*===============================================*\
 * Exported functions definitions
\*===============================================*/
void update_dp (a630::uart_protocol::tuya_data_point_t *dp)
{
    using namespace a630::uart_protocol;
    using namespace a630::mqtt_protocol;
    // ESP_LOGW (TAG,"-DP-");
    // ESP_LOG_BUFFER_HEXDUMP(TAG,dp, 20, ESP_LOG_INFO);
    bsh_sys::mqtt_protocol::mqtt_out_block_t block = {};
    if (!dp_to_mqtt_block(dp, &block)) return;  // its not used dp then
    bool critical_dp_changed = is_critical_dp_changed(bsh_sys::telem_storage::get_block(get_actor_id(block.actor)), &block);
    int old_value = bsh_sys::telem_storage::get_block(get_actor_id(block.actor))->value.in_int;
    bsh_sys::telem_storage::update_block(get_actor_id(block.actor), &block);

    if (critical_dp_changed)
    {
        ESP_LOGI (TAG, "== critical DP changed ==  actor: %c%c%c   old value: %02x   new: %02x", block.actor[0], block.actor[1], block.actor[2],
                    old_value , block.value.in_int);
        bsh_sys::telem_storage::push_telemetry_after_ms(PUSH_TELEM_DELAY, bsh_sys::mqtt_protocol::ORN_DEVICE);  
    }
}

 

void update_fung_firm_ver(uint8_t *fung_firm_ver)
{
    bsh_sys::mqtt_protocol::mqtt_out_block_t block = {};
    memcpy (block.actor, a630::mqtt_protocol::actor_code_to_string(a630::mqtt_protocol::FFV), 3);
    block.index = 0;
    block.capability = 0;
    block.action_type = bsh_sys::mqtt_protocol::REPLY;
    block.value_type = bsh_sys::mqtt_protocol::INT;

    block.value.in_bytes[0] = *fung_firm_ver;
    block.value.in_bytes[1] = *(fung_firm_ver + 1);
    block.value.in_bytes[2] = *(fung_firm_ver + 2);
    block.value.in_bytes[3] = 0;
    
    bsh_sys::telem_storage::update_block(a630::mqtt_protocol::FFV, &block);
}



void set_fung_product_id(char *fung_prod_id)
{
    if (strlen(fung_prod_id) > 25) 
    {
        ESP_LOGE (TAG,"too long prod id");
        return;
    }

    strcpy(fun_prod_id, fung_prod_id);
}



void update_fung_ota_status(a630::mqtt_protocol::fungene_ota_status_t status)
{
    bsh_sys::mqtt_protocol::mqtt_out_block_t block = {};
    memcpy (block.actor, a630::mqtt_protocol::actor_code_to_string(a630::mqtt_protocol::FFS), 3);
    block.index = 0;
    block.capability = 0;
    block.action_type = bsh_sys::mqtt_protocol::REPLY;
    block.value_type = bsh_sys::mqtt_protocol::INT;
    block.value.in_int = status;

    bsh_sys::telem_storage::update_block(a630::mqtt_protocol::FFS, &block);
}



const char *get_fung_product_id()
{
    return fun_prod_id;
}




/*===============================================*\
 * Local function definitions
\*===============================================*/
static uint32_t from_big_endian (uint8_t *data, uint8_t len)
{
    if (len > 4) 
    {
        ESP_LOGE (TAG, "too long value to convert");
        return 0;
    }

    uint32_t result = 0;
    uint8_t *by_byte = (uint8_t *)&result;

    switch (len)
    {
    case 4:
        *(by_byte) = *(data+3);
    case 3:
        *(by_byte + 1) = *(data+2);
    case 2:
        *(by_byte + 2) = *(data+1);
    case 1:
        *(by_byte + 3) = *data;
    default:
        break;

    }
    return result;
}



/**
 * Converts Tuya Data Point into Bork mqtt protocol actor block 
 */
static bool dp_to_mqtt_block(a630::uart_protocol::tuya_data_point_t *dp, bsh_sys::mqtt_protocol::mqtt_out_block_t *mqtt_out_block)
{
    using namespace a630::uart_protocol;
    using namespace bsh_sys::mqtt_protocol;
    using namespace a630::mqtt_protocol;

    bool not_used_dp = false;

    mqtt_out_block->index = 0;
    mqtt_out_block->capability = 0;
    mqtt_out_block->action_type = REPLY;

    // set actor and data type
    switch (dp->dpid)
    {

    case 0:   
        not_used_dp = true;
        break;
    case DP_SEND_TIME_STAMP:
    case DP_TOTAL_YEAR:
    case DP_TOTAL_MONTH:
    case DP_TOTAL_WEEK:
    case DP_SCENE_0:
    case DP_SCENE_1:
    case DP_SCENE_2:	
    case DP_SCENE_3:	
    case DP_SCENE_4:	
    case DP_SCENE_5:	
    case DP_LOCATION:
    case DP_BETTER:
    case DP_WEEK7:
    case DP_DAY_TOTAL:
    case DP_MONTH10:
    case DP_MONTH20:
    case DP_MONTH31:
    case DP_YEAR1:
    case DP_YEAR2:
    case DP_YEAR3:
    case DP_WORKING_TIME_END:  // not for sending to mqtt
        not_used_dp = true;
        // ESP_LOGI (TAG,"not used DP. dropped");
        break;


    case DP_MOD:
        memcpy (mqtt_out_block->actor, actor_code_to_string(MOD), 3);
        mqtt_out_block->value_type = INT;  // int coz no bool type in bork mqtt protocol
        break;
    case DP_AREA:
        memcpy (mqtt_out_block->actor, actor_code_to_string(ARA), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_OIL_LEVEL:
        memcpy (mqtt_out_block->actor, actor_code_to_string(WLP), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_CONCENTRATION: 	   
        memcpy (mqtt_out_block->actor, actor_code_to_string(ALP), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_SLEEP_TIME_START:
        memcpy (mqtt_out_block->actor, actor_code_to_string(STS), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_SLEEP_TIME_END:
        memcpy (mqtt_out_block->actor, actor_code_to_string(STE), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_SCENARIO:
        memcpy (mqtt_out_block->actor, actor_code_to_string(SCN), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_FAST_SMELL:
        memcpy (mqtt_out_block->actor, actor_code_to_string(FSM), 3);
        mqtt_out_block->value_type = INT;
        break;
    case 
        DP_FAST_SMELL_CTRL:
        memcpy (mqtt_out_block->actor, actor_code_to_string(FSC), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_FAST_SMELL_TIME:
        memcpy (mqtt_out_block->actor, actor_code_to_string(FST), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_FAST_SMELL_START_TIME:
        memcpy (mqtt_out_block->actor, actor_code_to_string(FSS), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_FAST_SMELL_REPEAT:
        memcpy (mqtt_out_block->actor, actor_code_to_string(FSR), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_DEVICE_STATE:
        memcpy (mqtt_out_block->actor, actor_code_to_string(STA), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_POSTPONE_START:
        memcpy (mqtt_out_block->actor, actor_code_to_string(STC), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_WORK_COUNT:
        memcpy (mqtt_out_block->actor, actor_code_to_string(WCN), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_WORK_TIME_CD:
        memcpy (mqtt_out_block->actor, actor_code_to_string(WTC), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_CLEAR_DATA:
        memcpy (mqtt_out_block->actor, actor_code_to_string(CLR), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_CLEAN:
        memcpy (mqtt_out_block->actor, actor_code_to_string(CLN), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_POWER_SAVE_MODE:
        memcpy (mqtt_out_block->actor, actor_code_to_string(SLP), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_OTA_STATE:
        ESP_LOGW (TAG, "============================");
        ESP_LOGW (TAG, "fungene ota status: %u %u %u %u", *dp->data, *(dp->data+1),*(dp->data+2),*(dp->data+3));
        ESP_LOGW (TAG, "============================");

        memcpy (mqtt_out_block->actor, actor_code_to_string(FFS), 3);
        mqtt_out_block->value_type = INT;
        break;
    case DP_FIRM_VER:
        memcpy (mqtt_out_block->actor, actor_code_to_string(FFV), 3);
        mqtt_out_block->value_type = INT;
        break;
    }

    if (not_used_dp) return false;

   // set values 
    switch (dp->dpid)
    {
    case DP_FAST_SMELL_REPEAT:      // tuya data - in string format
        mqtt_out_block->value.in_int = fast_smell_repeat_to_int(dp->data, dp->data_len);
        break;
    case DP_MOD:                  // booleans
    case DP_FAST_SMELL_CTRL:
        mqtt_out_block->value.in_int = *dp->data;
        break;
    case DP_OTA_STATE:            // byte - coding
        mqtt_out_block->value.in_int = *(dp->data + 2);  // i.e. taking third byte of data
        break;
    default:                      // for everyone else - in int
        mqtt_out_block->value.in_int = from_big_endian(dp->data,dp->data_len);
        break;
    }

    return true;
}



static int fungene_color_to_int (uint8_t *data, uint8_t len)
{
    int result = 0;
    for (size_t i = 0; i < 6; i++)
    {
        if (data[i+1] == '0') result += 0 * pow(16, 5-i);
        if (data[i+1] == '1') result += 1 * pow(16, 5-i);
        if (data[i+1] == '2') result += 2 * pow(16, 5-i);
        if (data[i+1] == '3') result += 3 * pow(16, 5-i);
        if (data[i+1] == '4') result += 4 * pow(16, 5-i);
        if (data[i+1] == '5') result += 5 * pow(16, 5-i);
        if (data[i+1] == '6') result += 6 * pow(16, 5-i);
        if (data[i+1] == '7') result += 7 * pow(16, 5-i);
        if (data[i+1] == '8') result += 8 * pow(16, 5-i);
        if (data[i+1] == '9') result += 9 * pow(16, 5-i);
        if (data[i+1] == 'a' || data[i+1] == 'A') result += 10 * pow(16, 5-i);
        if (data[i+1] == 'b' || data[i+1] == 'B') result += 11 * pow(16, 5-i);
        if (data[i+1] == 'c' || data[i+1] == 'C') result += 12 * pow(16, 5-i);
        if (data[i+1] == 'd' || data[i+1] == 'D') result += 13 * pow(16, 5-i);
        if (data[i+1] == 'e' || data[i+1] == 'E') result += 14 * pow(16, 5-i);
        if (data[i+1] == 'f' || data[i+1] == 'F') result += 15 * pow(16, 5-i);
    }
    return result;
}



static int fast_smell_repeat_to_int (uint8_t *data, uint8_t len)
{
    int result = 0;

    char tmp[len + 1] = {};
    memcpy (tmp, data, len);
    const char *d[] = {"1","2","3","4","5","6","7"};
    if (strstr (tmp, d[0])) result |= 1 << 6; // monday
    if (strstr (tmp, d[1])) result |= 1 << 5; // t
    if (strstr (tmp, d[2])) result |= 1 << 4; // w
    if (strstr (tmp, d[3])) result |= 1 << 3; // th
    if (strstr (tmp, d[4])) result |= 1 << 2; // fri
    if (strstr (tmp, d[5])) result |= 1 << 1; // sat
    if (strstr (tmp, d[6])) result |= 1 << 0; // sun

    return result;
}



static bool is_critical_dp_changed(bsh_sys::mqtt_protocol::mqtt_out_block_t *old_blk, 
                    bsh_sys::mqtt_protocol::mqtt_out_block_t *new_blk)
{
    using namespace a630::mqtt_protocol;
    uint8_t actor_id = get_actor_id(new_blk->actor);

    if (old_blk->actor[0] == 0) return false;  // coz its first update of this actor

    if (actor_id == MOD ||
        actor_id == WLP ||
        actor_id == ALP ||
        actor_id == FSM ||
        actor_id == CLN ||
        actor_id == FFS ||
        actor_id == ARA ||
        actor_id == STS ||
        actor_id == STE ||
        actor_id == FSC ||
        actor_id == WTC ||
        actor_id == STC ||
        actor_id == STA)
    {
        bool block_changed = (memcmp(&new_blk->value, &old_blk->value, 4) != 0);
        return block_changed;
    }

    return false;
}



}  // namespace end
 