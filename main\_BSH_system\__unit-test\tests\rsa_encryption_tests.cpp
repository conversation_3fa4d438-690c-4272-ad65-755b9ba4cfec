#include <string.h>
#include <iostream>
#include <filesystem>

#include "CppUTest/TestHarness.h"

#include "rsa_encryption.h"

// #include "rsa_encryption.cpp"

// #include "rsa_encryption.h"
// #include "ctr_drbg.h"
// #include "entropy.h"
// #include "error.h"
// #include "sha256.h"
// #include "ctr_drbg.h"



// extern const unsigned char RSA_public_key_start[]   asm("_binary_esp32_rsa_pub_pair_start"); 
// extern const unsigned char RSA_public_key_end[]   asm("_binary_esp32_rsa_pub_pair_end");
// extern const unsigned char RSA_private_key_start[]   asm("_binary_esp32_rsa_priv_pair_start");
// extern const unsigned char RSA_private_key_end[]   asm("_binary_esp32_rsa_priv_pair_end");
unsigned char RSA_public_key_start[2000]        asm("_binary_esp32_rsa_pub_pair_start"); 
unsigned char *RSA_public_key_end          asm("_binary_esp32_rsa_pub_pair_end");
unsigned char RSA_private_key_start[2000]       asm("_binary_esp32_rsa_priv_pair_start");
unsigned char *RSA_private_key_end         asm("_binary_esp32_rsa_priv_pair_end");

extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}

static void read_public_key();
static void read_private_key();


// guid format: bork_device_q781_cac4b175-fffe-4b19-8359-456d8279b87b
static bool init_result = 0;
TEST_GROUP(rsa)
{
    void setup()
    {
        MemoryLeakWarningPlugin::saveAndDisableNewDeleteOverloads();
        read_public_key();
        read_private_key();

        init_result = bsh_sys::rsa::init();
    }

    void teardown()
    {
        MemoryLeakWarningPlugin::restoreNewDeleteOverloads();
    }
};



TEST(rsa, init)
{
    CHECK (init_result);
    CHECK(bsh_sys::rsa::get_init_status());
}



TEST (rsa, sign_verify)
{
    using namespace bsh_sys::rsa;
    const char *source_string = "asdfasfdsadfsddfsadfsdfsdfasdffsd";

    uint8_t signature[SIGNATURE_SIZE] = {};

    sign_data((const unsigned char *)source_string, strlen(source_string), signature);

    int res =  verify_signature ((const unsigned char *)source_string, strlen(source_string), (const unsigned char *)signature);

    CHECK(res == 0);
    CHECK(signature[0] != 0);
}



TEST (rsa, encrypt_decrypt)
{
    using namespace bsh_sys::rsa;
    const char *source_string = "asdfasfdsadfsddfsadfsdfsdfasdffsd";

    unsigned char encrypt_buff[SIGNATURE_SIZE] = {};
    int res = encrypt_data(source_string, strlen(source_string), encrypt_buff);
    CHECK(res == 0);

    unsigned char decrypt_buff[SIGNATURE_SIZE] = {};
    res = decrypt_data ((const unsigned char *)encrypt_buff, SIGNATURE_SIZE, decrypt_buff, SIGNATURE_SIZE);
    CHECK(res == 0);

    MEMCMP_EQUAL(source_string, decrypt_buff, strlen(source_string));
}



TEST(rsa, calculate_sha256_hash)
{
    using namespace bsh_sys::rsa;
    const char *source_string = "asdfasfdsadfsddfsadfsdfsdfasdffsd";
    const uint8_t correct_result[] = {0xa8, 0xb7, 0xaf, 0x41, 0xb9, 0xc8, 0xce, 0xe6, 0x38, 0xbd, 0x55, 0x98, 0x94, 0x9a, 0xcc, 0xf9, 0x8c, 0x1b, 0x5a, 0x13, 0x1a, 0x34, 0x2c, 0xbf, 0x34, 0x28, 0xb2, 0x23, 0x16, 0xcd, 0x1d, 0xd5};
    sha_256_hash_t res;

    int res_code = calculate_sha256_hash((const unsigned char*)source_string, strlen(source_string), &res);

    CHECK(res_code == 0);
    MEMCMP_EQUAL(correct_result, res.hash, 256/8);
}



TEST(rsa, compare_sha256_hashes)
{
    using namespace bsh_sys::rsa;
    const char *source_string = "asdfasfdsadfsddfsadfsdfsdfasdffsd";
    sha_256_hash_t res1;
    sha_256_hash_t res2;

    int res_code = calculate_sha256_hash((const unsigned char*)source_string, strlen(source_string), &res1);
    CHECK(res_code == 0);
    res_code = calculate_sha256_hash((const unsigned char*)source_string, strlen(source_string), &res2);
    CHECK(res_code == 0);

    CHECK(compare_sha256_hashes((char *)res1.hash, (char*)res2.hash));
}



TEST(rsa, get_random)
{
    uint32_t prev_rnd = 0;
    for (size_t i = 0; i < 10; i++)
    {
        uint32_t rnd = bsh_sys::rsa::get_random();
        CHECK(prev_rnd != rnd);
        prev_rnd = rnd;
    }
}





#include <fstream>
#include <string>



static void print_key(char *start, char* end)
{
    printf("\n key:\n");
    for (size_t i = 0; i < end - start; i++)
    {
        printf("%02x", *(start+i));
        if (i != 0 && i%16 == 0) printf("\n");
    }
    printf("\n key end \n \n");
}




static void read_public_key()
{
    using namespace std;
    using namespace bsh_sys::rsa;
    
    string file_name("esp32_rsa_pub_pair");
    ifstream ifs(file_name.c_str(), ios::in | ios::binary | ios::ate);

    ifstream::pos_type file_size = ifs.tellg();
    ifs.seekg(0, ios::beg);

    // std::cout<< std::filesystem::current_path();
    // printf("\n key file size: %i \n", file_size);

    ifs.read((char *)RSA_public_key_start, file_size);
    RSA_public_key_end = RSA_public_key_start + file_size +1;

    // print_key((char *)RSA_public_key_start, (char *)RSA_public_key_end);
}



static void read_private_key()
{
    using namespace std;
    using namespace bsh_sys::rsa;
    
    string file_name("esp32_rsa_priv_pair");
    ifstream ifs(file_name.c_str(), ios::in | ios::binary | ios::ate);

    ifstream::pos_type file_size = ifs.tellg();
    ifs.seekg(0, ios::beg);

    // printf("\n key file size: %i \n", file_size);

    ifs.read((char *)RSA_private_key_start, file_size);
    RSA_private_key_end = RSA_private_key_start + file_size +1;

    // print_key((char *)RSA_private_key_start, (char *)RSA_private_key_end);
}










