#include <stdint.h>
#include <stdlib.h>
#include <string.h>

#include "esp_log.h"
#include "esp_system.h"
#include "esp_timer.h"

#include "telemetry_storage.h"
#include "date_and_time.h"
#include "mqtt_logs.h"



namespace bsh_sys::telem_storage {



#define DEFAULT_TELEM_TIME 30 //s



/* ======= local object declarations =========*/
static const char *TAG = "-telem store-";
static bool initialized = false;
static bsh_sys::mqtt_protocol::mqtt_out_block_t *blocks_arr = NULL;
static uint8_t blocks_qty;
static esp_timer_handle_t telem_timer;
static uint16_t regular_telem_time;
static esp_timer_handle_t delayed_push_timer = NULL;
static bsh_sys::mqtt_protocol::origin_code_t delayed_orn;
static bool waiting_for_delayed_push = false;



/* ======= local function declarations ========= */
static void tlm_timer_handler(void *);
static bool check_init_done();
static void log_telemetry();
static void set_ORN_to (bsh_sys::mqtt_protocol::origin_code_t origin);
static void set_RPL(uint8_t *code);  // if code is NULL, doesnt do anything



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
void init (uint8_t blks_qty)
{
    if (initialized) return;

    using namespace bsh_sys::mqtt_protocol;

    blocks_qty = blks_qty;
    blocks_arr = (mqtt_out_block_t *)malloc(sizeof(mqtt_out_block_t)*blocks_qty);
    if (blocks_arr == NULL)
    {
        ESP_LOGE(TAG, "cant allocate mqtt blocks arr");
        esp_restart();
    }
    memset (blocks_arr, 0, sizeof(mqtt_out_block_t)*blocks_qty);

    // timer init
    const esp_timer_create_args_t timer_args = {tlm_timer_handler, 0, {}, 0};
	int ret = 0;
	if((ret = esp_timer_create(&timer_args, &telem_timer)) != ESP_OK) {
        ESP_LOGE(TAG,"cant create timer %d", ret);
        esp_restart();
    }
    regular_telem_time = DEFAULT_TELEM_TIME;
    esp_timer_start_periodic (telem_timer, regular_telem_time * 1000000);	

    initialized = true;
}



void update_block (uint8_t block_index, bsh_sys::mqtt_protocol::mqtt_out_block_t *block)
{
    if (!check_init_done()) { return; }

    if (block_index >= blocks_qty)
    {
        ESP_LOGE(TAG,"too big index: %u", block_index);
        return;
    }
    memcpy(blocks_arr + block_index, block, sizeof(bsh_sys::mqtt_protocol::mqtt_out_block_t));
}



void clear_block (uint8_t block_index)
{
    memset (blocks_arr + block_index, 0, sizeof (bsh_sys::mqtt_protocol::mqtt_out_block_t));
}



void push_telemetry(bsh_sys::mqtt_protocol::origin_code_t origin, uint8_t *RPL_code)
{
    using namespace bsh_sys::mqtt_protocol;

    if (!check_init_done()) return; 
    if (waiting_for_delayed_push) return; 

    if (origin != ORN_TIME) reset_telem_timer();

    set_ORN_to(origin);

    if (RPL_code != NULL) 
    {
        set_RPL(RPL_code);
    } else if (origin == ORN_BROKER_CMD) {
        uint8_t tmp_code = 0;
        set_RPL(&tmp_code);
    } else {
        clear_block(blocks_qty - SYS_ACTORS_QTY + RPL);
    }    

    log_telemetry();
    send_blocks(blocks_arr, blocks_qty, DEFAULT_QOS);

    // remove RPL block
    clear_block (blocks_qty - SYS_ACTORS_QTY + RPL);

    bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "telemetry sent", NULL, 0);
}



void push_single_block(uint8_t block_id)
{
    bsh_sys::mqtt_protocol::send_blocks(&blocks_arr[block_id], 1, DEFAULT_QOS);
}



static void push_telemetry_wrapper(void *)
    {
        waiting_for_delayed_push = false;
        push_telemetry(delayed_orn, NULL);
        delayed_orn = bsh_sys::mqtt_protocol::ORN_USER;    // to clear ORN_BROKER_CMD , which has high priority
    }

void push_telemetry_after_ms(uint32_t delay_ms, bsh_sys::mqtt_protocol::origin_code_t origin)
{
    using namespace bsh_sys::mqtt_protocol;

    if (origin == ORN_BROKER_CMD) delayed_orn = origin;

    if (waiting_for_delayed_push) 
    { 
        return; 
    };

    if (delayed_push_timer == NULL)
    {
        const esp_timer_create_args_t DelayedTimerArgs = {push_telemetry_wrapper, 0, {}, 0, 0};
        ESP_ERROR_CHECK(esp_timer_create(&DelayedTimerArgs, &delayed_push_timer));
    } else {
        esp_timer_stop(delayed_push_timer);
    }
    
    if (delayed_orn != ORN_BROKER_CMD) delayed_orn = origin;     // so don't owerwrite broker cmd. its cleared only after T. sent
    esp_timer_start_once(delayed_push_timer, ((uint64_t)delay_ms)*1000);
    waiting_for_delayed_push = true;
}



void stop_regular_telemetry()
{
    if (!check_init_done()) { return; }

    esp_timer_stop(telem_timer);
}



void start_regular_telemetry()
{
    if (!check_init_done()) { return; }

    esp_timer_stop(telem_timer);
    esp_timer_start_periodic(telem_timer, regular_telem_time * 1000000);	
}



void reset_telem_timer()
{
    if (!check_init_done()) { return; }

    esp_timer_stop(telem_timer);
    esp_timer_start_periodic(telem_timer, regular_telem_time * 1000000);
}



void set_regular_telemetry_time (uint16_t seconds)
{
    if (!check_init_done()) { return; }

    esp_timer_stop(telem_timer);
    regular_telem_time = seconds;
    esp_timer_start_periodic(telem_timer, regular_telem_time * 1000000);
}



bsh_sys::mqtt_protocol::mqtt_out_block_t *get_block(uint8_t block_n)
{
    if (!check_init_done()) { return NULL; }

    if (block_n >= blocks_qty)
    {
        ESP_LOGE (TAG,"wrong block N");
        return blocks_arr;
    }
    return blocks_arr + block_n;
}



/*===============================================*\
 * Local function definitions
\*===============================================*/
static void tlm_timer_handler(void *)
{
    push_telemetry(bsh_sys::mqtt_protocol::ORN_TIME, NULL);
}



static bool check_init_done()
{
    if (!initialized)
    {
        ESP_LOGE (TAG,"not initialized");
        return false;
    }
    return true;
}



static void log_telemetry()
{
    const bsh_sys::date_time::date_and_time_t *time = bsh_sys::date_time::get_date_time();

    printf("time: %u:%u:%u   ", time->hour, time->min, time->sec);
    for (size_t i = 0; i < blocks_qty; i++)
    {
        if ((blocks_arr + i)->actor[0] == 0) continue;
        printf ("%c%c%c %i   " , (blocks_arr + i)->actor[0], (blocks_arr + i)->actor[1], (blocks_arr + i)->actor[2], (blocks_arr + i)->value.in_int);
    }
    printf ("\n");
}



static void set_ORN_to (bsh_sys::mqtt_protocol::origin_code_t origin)
{
    using namespace bsh_sys::mqtt_protocol;

    mqtt_out_block_t block = {
        .actor = {'O','R','N'},
        .index = 0,
        .capability = 0,
        .action_type = REPLY,
        .value_type = INT,
        .value = {.in_int = origin},
    };

    update_block (blocks_qty - bsh_sys::mqtt_protocol::SYS_ACTORS_QTY + ORN, &block);

    // ESP_LOGW (TAG, "========= ORN: %u", origin);
}



static void set_RPL(uint8_t *code)
{
    if (code == NULL) return;

    using namespace bsh_sys::mqtt_protocol;

    mqtt_out_block_t block = {
        .actor = {'R','P','L'},
        .index = 0,
        .capability = 0,
        .action_type = REPLY,
        .value_type = INT,
        .value = {.in_int = *code},
    };

    update_block (blocks_qty - bsh_sys::mqtt_protocol::SYS_ACTORS_QTY + RPL, &block);
}



} // end of bsh_sys::telem_storage  namespace
