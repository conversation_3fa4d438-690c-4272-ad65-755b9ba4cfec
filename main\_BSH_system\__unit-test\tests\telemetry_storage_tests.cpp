#include <string.h>

#include "CppUTest/TestHarness.h"

#include "telemetry_storage.h"
#include "esp_timer.h"



extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}


#define STORAGE_SIZE 12

static bsh_sys::mqtt_protocol::mqtt_out_block_t *send_result__blocks_ptr = NULL;
static uint8_t send_results__blocks_qty = 200;
static uint8_t send_results__qos_level = 50;
static bsh_sys::mqtt_protocol::mqtt_out_block_t send_result__RPL_block;
static bool send_blocks_was_called = false;

// static void print_blocks();

namespace bsh_sys::mqtt_protocol
{
    void send_blocks (mqtt_out_block_t *blocks, uint8_t blocks_qty, uint8_t qos_level)
    {
        send_result__blocks_ptr = blocks;
        send_results__blocks_qty = blocks_qty;
        send_results__qos_level = qos_level;

        memcpy(&send_result__RPL_block, blocks + STORAGE_SIZE - 4, sizeof(bsh_sys::mqtt_protocol::mqtt_out_block_t));

        send_blocks_was_called = true;
        // print_blocks();
    }
}



static bsh_sys::mqtt_protocol::mqtt_out_block_t block =
{
    .actor = {'A','C','T'},
    .index = 0,
    .capability = 0,
    .action_type = bsh_sys::mqtt_protocol::REPLY,
    .value_type = bsh_sys::mqtt_protocol::INT,
};




TEST_GROUP(set_get_push_clear)
{
    void setup()
    {
        MemoryLeakWarningPlugin::saveAndDisableNewDeleteOverloads();
        bsh_sys::telem_storage::init(STORAGE_SIZE);

        block.value.in_int = 50;
    }

    void teardown()
    {
        clear_esp_timer_mock();
        MemoryLeakWarningPlugin::restoreNewDeleteOverloads();
    }
};

TEST(set_get_push_clear, update_block_copies_data)
{
    using namespace bsh_sys::mqtt_protocol;
    using namespace bsh_sys::telem_storage;

    update_block(0, &block);

    MEMCMP_EQUAL(get_block(0), &block,  sizeof(mqtt_out_block_t));
}

TEST(set_get_push_clear, clear_block_clears_data)
{
    using namespace bsh_sys::mqtt_protocol;
    using namespace bsh_sys::telem_storage;

    update_block(0, &block);
    clear_block(0);

    mqtt_out_block_t clear_block;
    memset(&clear_block, 0, sizeof(mqtt_out_block_t));
    MEMCMP_EQUAL(get_block(0), &clear_block,  sizeof(mqtt_out_block_t));
}

TEST(set_get_push_clear, push_sends_blocks) 
// for now T_storage module has some knowledge about blocks:
// it knows that ORN block is N3 in system-blocks list. 
// and module makes this block and puts it into list.
// also it adds RPL block based on push_telemetry() call parameter
// need to fix this later
{
    using namespace bsh_sys::mqtt_protocol;
    using namespace bsh_sys::telem_storage;

    // single block
    update_block(0, &block);

    push_telemetry(ORN_DEVICE, NULL);

    // check T was sent, proper size and qos level
    CHECK (get_block(0) == send_result__blocks_ptr);
    CHECK (send_results__blocks_qty == STORAGE_SIZE); // storage sends all blocks, including empty ones
    CHECK (send_results__qos_level == 0);

    // check actors are correct
    CHECK (send_result__blocks_ptr[0].actor[0] == 'A' && send_result__blocks_ptr[0].actor[1] == 'C' && send_result__blocks_ptr[0].actor[2] == 'T');
    // ORN block is correct
    CHECK (send_result__blocks_ptr[STORAGE_SIZE - 5].actor[0] == 'O' && send_result__blocks_ptr[STORAGE_SIZE - 5].actor[1] == 'R' && send_result__blocks_ptr[STORAGE_SIZE - 5].actor[2] == 'N');
    CHECK (send_result__blocks_ptr[STORAGE_SIZE - 5].value.in_int == ORN_DEVICE);


    // multiple blocks
    update_block(0, &block);
    update_block(1, &block);
    update_block(2, &block);
    update_block(3, &block);
    update_block(4, &block);

    // test RPL and ORN blocks are correct
    push_telemetry(ORN_BROKER_CMD, NULL);

    // print_blocks();

    for (size_t i = 0; i < 5; i++)
    {
        CHECK (send_result__blocks_ptr[i].actor[0] == 'A' && send_result__blocks_ptr[i].actor[1] == 'C' && send_result__blocks_ptr[i].actor[2] == 'T');
    }
    // ORN block is correct
    CHECK (send_result__blocks_ptr[STORAGE_SIZE - 5].actor[0] == 'O' && send_result__blocks_ptr[STORAGE_SIZE - 5].actor[1] == 'R' && send_result__blocks_ptr[STORAGE_SIZE - 5].actor[2] == 'N');
    CHECK (send_result__blocks_ptr[STORAGE_SIZE - 5].value.in_int == ORN_BROKER_CMD);
    
    // RPL block added
    // RPL cant be checked via pointer to telemetry coz RPL block is cleaned up right after calling push_telemetry();
    CHECK (send_result__RPL_block.actor[0] == 'R' && send_result__RPL_block.actor[1] == 'P' && send_result__RPL_block.actor[2] == 'L');
    CHECK (send_result__RPL_block.value.in_int == 0);
    
}





TEST_GROUP (regular_telemetry)
{
    void setup()
    {
        MemoryLeakWarningPlugin::saveAndDisableNewDeleteOverloads();
        bsh_sys::telem_storage::init(STORAGE_SIZE);

        block.value.in_int = 30;
    }

    void teardown()
    {
        clear_esp_timer_mock();
        MemoryLeakWarningPlugin::restoreNewDeleteOverloads();
    }
};


#define DEFAULT_T_TIME_FROM_T_STORAGE_CPP 30 //s
TEST(regular_telemetry, sends_T_regulary) 
{
    using namespace bsh_sys::mqtt_protocol;
    using namespace bsh_sys::telem_storage;
    update_block(0, &block);

    for (size_t i = 1; i < 5; i++)
    {
        send_blocks_was_called = false;
        call_timer_cb_if_its_due_time(DEFAULT_T_TIME_FROM_T_STORAGE_CPP * i * 1000000);
        CHECK(send_blocks_was_called);
    }
    
    // change time 
    set_regular_telemetry_time(20);
    uint64_t start_time = get_current_time();
    for (size_t i = 1; i < 5; i++)
    {
        send_blocks_was_called = false;
        call_timer_cb_if_its_due_time(start_time + 20 * i * 1000000);
        CHECK(send_blocks_was_called);
    }

    // stop telemetry works
    stop_regular_telemetry();
    start_time = get_current_time();
    for (size_t i = 1; i < 5; i++)
    {
        send_blocks_was_called = false;
        call_timer_cb_if_its_due_time(start_time + 20 * i * 1000000);
        CHECK(!send_blocks_was_called);
    }

    // resume telemetry works
    start_regular_telemetry();
    start_time = get_current_time();
    for (size_t i = 1; i < 5; i++)
    {
        send_blocks_was_called = false;
        call_timer_cb_if_its_due_time(start_time + 20 * i * 1000000);
        CHECK(send_blocks_was_called);
    }

    // reset timer works
    send_blocks_was_called = false;
    call_timer_cb_if_its_due_time(get_current_time() + 10*1000000);
    CHECK(!send_blocks_was_called);
    reset_telem_timer();
    call_timer_cb_if_its_due_time(get_current_time() + 10*1000000);
    CHECK(!send_blocks_was_called);
    call_timer_cb_if_its_due_time(get_current_time() + 10*1000000);
    CHECK(send_blocks_was_called);
}




/****************************
 *         helpers
 ****************************/
static void print_blocks()
{
    printf("\nprinting blocks: \n");
    printf("   N    actor   action type   value type    value \n");
    for (size_t i = 0; i < STORAGE_SIZE; i++)
    {
        if (send_result__blocks_ptr[i].actor[0] == 0)
        {
            printf ("   %li    --- \n", i);
        } else {

        printf ("   %li    %c%c%c     %02x            %02x            %i\n", i, 
                    send_result__blocks_ptr[i].actor[0],
                    send_result__blocks_ptr[i].actor[1],
                    send_result__blocks_ptr[i].actor[2],
                    send_result__blocks_ptr[i].action_type,
                    send_result__blocks_ptr[i].value_type,
                    send_result__blocks_ptr[i].value.in_int
                    );
        }
    }
}