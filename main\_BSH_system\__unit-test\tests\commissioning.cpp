#include "CppUTest/TestHarness.h"

#include "commissioning.h"
#include "nvs.h"
#include "esp_random.h"
#include "device_id.h"
#include "esp_bt_device.h"



extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}

TEST_GROUP(commissioning)
{
    void setup()
    {
        MemoryLeakWarningPlugin::saveAndDisableNewDeleteOverloads();
        bsh_sys::commiss::init();

    }

    void teardown()
    {
        MemoryLeakWarningPlugin::restoreNewDeleteOverloads();
    }
};



TEST(commissioning, correct_values)
{
    using namespace bsh_sys::commiss;

    set_wifi_ssid("wifi ssid");
    set_wifi_passw("wifi passw");

    STRCMP_EQUAL("wifi ssid", get_wifi_ssid());
    STRCMP_EQUAL("wifi passw", get_wifi_passw());



    set_mqtt_user_id("mqtt_user_id");  
    set_mqtt_passw("mqtt_passw"); 

    STRCMP_EQUAL("mqtt_user_id", get_mqtt_user_id());
    STRCMP_EQUAL("mqtt_passw", get_mqtt_passw());

    // get_dev_id_as_string  test
    uint8_t *id = bsh_sys::devid::get_device_id();
    char id_string[21] = {};
    for (size_t i = 0; i < 10; i++)
    {
        snprintf (id_string + i*2, 3 , "%02x", id[i]);
    }
    STRCMP_EQUAL(id_string, get_dev_id_as_string());

    set_balancer_link("balancer_link");
    STRCMP_EQUAL("balancer_link", get_balancerlink());


    // check data loaded properly after restart
    bsh_sys::commiss::init();
    STRCMP_EQUAL("wifi ssid", get_wifi_ssid());
    STRCMP_EQUAL("wifi passw", get_wifi_passw());
    STRCMP_EQUAL("mqtt_user_id", get_mqtt_user_id());
    STRCMP_EQUAL("mqtt_passw", get_mqtt_passw());
    STRCMP_EQUAL("balancer_link", get_balancerlink());

    clean_data();
}



