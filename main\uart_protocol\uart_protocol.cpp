#include <string.h>

#include "esp_log.h"
#include "esp_timer.h"

#include "uart_protocol.h"
#include "mqtt_logs.h"



namespace a630::uart_protocol
{



/* ======= local object declarations =========*/
static const char *TAG = "-uart_protocol-";



/* ======= local function declarations ========= */
uint8_t pckt_check_sum(const uint8_t *pckt, uint16_t pckt_size);
static void add_two_bytes_in_big_endian(uint8_t *add_to, const uint8_t *in);
static bool check_dp_id_matches_its_data_type (data_point_id_t id, tuya_data_type_t type);



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
parsing_errs_t parse_packet(const uint8_t *data, uint16_t data_len, tuya_uart_packet_t *out)
{
    if (*data != HEADER_0 || *(data+1) != HEADER_1)
    {
        ESP_LOGW(TAG,"wrong header");
        return PCKT_ERR_WRONG_HEADER;
    }

    uint16_t pkt_length = *(data+4)*256 + *(data+5);
    if (data_len - TUYA_PACKET_SERVICE_BYTES_QTY < pkt_length)  // if !>! -its two tuya packets in single uart data block. its treated below 
    {
        ESP_LOGI(TAG,"    cut packet");
        return PCKT_ERR_WRONG_LENGTH;
    }  

    if (pckt_check_sum(data, pkt_length + TUYA_PACKET_SERVICE_BYTES_QTY - 1) != *(data + pkt_length + TUYA_PACKET_SERVICE_BYTES_QTY - 1))
    {
        ESP_LOGW(TAG,"wrong checksum. != %02x", pckt_check_sum(data, data_len - 1));
        return PCKT_ERR_WRONG_CHECKSUM;
    }

    out->header[0] = *data;
    out->header[1] = *(data+1);
    out->version = *(data+2);
    out->command = *(data+3);
    out->data_len = *(data+4)*256 + *(data+5);
    out->data = (uint8_t *)(data+6);
    out->checksum = *(data + data_len - 1);

    bool more_than_one_pkt = data_len - TUYA_PACKET_SERVICE_BYTES_QTY > pkt_length;
    if (more_than_one_pkt) 
    {
        return PCKT_WARN_UNPARCED_DATA_LEFT;
    }
    return PCKT_NO_ERR;
}



parsing_errs_t parse_data_point(const uint8_t *data, uint16_t data_len, tuya_data_point_t *out)
{
    if (data_len < 5) return PCKT_ERR_WRONG_LENGTH;
    uint16_t data_length = *(data+2)*256 + *(data+3);
    if (data_len < 1+1+2+ data_length) return PCKT_ERR_WRONG_LENGTH;

    out->dpid = *data;
    out->data_type = *(data+1);
    if (!check_dp_id_matches_its_data_type((data_point_id_t)out->dpid, (tuya_data_type_t)out->data_type)) return PCKT_WRONG_TYPE;
    
    out->data_len = *(data+2)*256 + *(data+3);
    out->data = (uint8_t *)(data + 4);
    return PCKT_NO_ERR;
}



uint8_t * find_next_header(const uint8_t *data, uint16_t len)
{
    for (size_t i = 0; i < len - 2; i++)
    {
        if ( *(data+i) == HEADER_0 && *(data+i+1) == HEADER_1) return (uint8_t *)data+i;
    }

    return NULL;
}



void out_pckt_init(tuya_out_uart_packet_t *pkt)
{
    pkt->header[0] = HEADER_0;
    pkt->header[1] = HEADER_1;
    pkt->version = UART_PROTOCOL_VERSION;
    pkt->command = TY_SET;
    pkt->data_len[0] = 0;
    pkt->data_len[1] = 0;
}



void out_pkt_add_dp(tuya_out_uart_packet_t *pkt, const tuya_data_point_t *dp)
{
    uint8_t shift = 2 + 1 + 1 + 2 + pkt->data_len[1] + pkt->data_len[0]*256; // header + version + command + data_len + dps laready in
    uint8_t *write_to = (uint8_t *)pkt + shift;

    *write_to = dp->dpid;
    *(write_to + 1) = dp->data_type;
    // data length
    if (dp->data_type == TDT_INT)
    {
        *(write_to+2) = 0x00;
        *(write_to+3) = 0x04;
    } else {
        *(write_to+2) = dp->data_len / 256;
        *(write_to+3) = dp->data_len % 256;
    }

    memcpy(write_to+4, dp->data, *(write_to+3) + *(write_to+2) * 256); // dp data is already in big endian

    uint8_t to_add[2] = {0x00, 0x04};  // 4 = header + version + command + data_len[2]
    add_two_bytes_in_big_endian(pkt->data_len, to_add);
    add_two_bytes_in_big_endian(pkt->data_len, write_to + 2);
}



void out_pkt_close(tuya_out_uart_packet_t *pkt)
{
    uint16_t data_len = pkt->data_len[1] + pkt->data_len[0]*256;
    *(pkt->data + data_len) = pckt_check_sum((const uint8_t *)pkt, 6 + data_len);
}



void make_single_dp_packet(tuya_out_uart_packet_t *pkt, const tuya_data_point_t *dp)
{
    out_pckt_init(pkt);
    out_pkt_add_dp(pkt, dp);
    out_pkt_close(pkt);
}



uint16_t get_dp_length(const tuya_data_point_t *dp)
{
    uint16_t res = 1 + 1 + 2;  //id byte, data type byte, data size 2 bytes
    switch (dp->data_type)
    {
        case TDT_RAW:
        case TDT_BOOL:
        case TDT_STRING:
        case TDT_ENUM:
        case TDT_BITMAP:
        return res + dp->data_len;

        case TDT_INT:
        return res + 4;    
    
    default:
        return 0;
    } 
    return 1 + 1 + 2 + dp->data_len;
}



uint16_t get_tuya_pkt_length(const tuya_out_uart_packet_t *pkt)
{
    return pkt->data_len[1] + pkt->data_len[0]*256 + 7;
}



const char * dp_id_to_string(data_point_id_t dp_id)
{
    switch (dp_id)
    {
    case DP_MOD: return "turn on";
    case DP_POWER_SAVE_MODE: return "working mode";
    case DP_AREA: return "area";
    case DP_LOCATION: return "location";
    case DP_SEND_TIME_STAMP: return "get time stamp";
    case DP_TOTAL_YEAR: return "year";
    case DP_TOTAL_MONTH: return "month";
    case DP_TOTAL_WEEK: return "week";
    case DP_BETTER: return "better?";
    case DP_OIL_LEVEL: return "oil lvl";
    case DP_CONCENTRATION: return "concentration";
    case DP_SLEEP_TIME_START: return "time start";
    case DP_SLEEP_TIME_END: return "time end";
    case DP_SCENARIO : return "scenario";
    case DP_WEEK7 : return "week7?";
    case DP_DAY_TOTAL : return "day total";
    case DP_FAST_SMELL: return "fast smell";
    case DP_FAST_SMELL_CTRL: return "fast smell ctrl";
    case DP_FAST_SMELL_TIME: return "smell time";
    case DP_FAST_SMELL_START_TIME: return "working start time";
    case DP_WORKING_TIME_END: return "working end time";
    case DP_FAST_SMELL_REPEAT: return "fast smell repeat";
    case DP_MONTH10: return "oil consumption per month";
    case DP_DEVICE_STATE: return "device state";
    case DP_MONTH20: return "month20";
    case DP_MONTH31: return "month31";
    case DP_YEAR1: return "year1";
    case DP_YEAR2: return "year2";
    case DP_YEAR3: return "year3";
    case DP_POSTPONE_START: return "pump_off_time";
    case DP_WORK_COUNT: return "work count";
    case DP_WORK_TIME_CD: return "pump_on time";
    case DP_CLEAR_DATA: return "clean data";
    case DP_CLEAN : return "clean";
    case DP_SET_WIFI_SSID: return "wifi ssid";
    case DP_SET_WIFI_PSWD: return "wifi pswd";
    case DP_OTA_STATE: return "OTA state";

    default:
    return "no such dp_id";
    }
}



// don't include checksum byte itself into the packet to check
uint8_t pckt_check_sum(const uint8_t *pckt, uint16_t pckt_size)
{
    uint16_t sum = 0;
    for (size_t i = 0; i < pckt_size; i++)
    {
        sum += *(pckt+i);
    }

    return sum % 256;
}



void log_uart_packet(tuya_uart_packet_t *pkt)
{   
    ESP_LOGI (TAG,"    PK  hdr: 0x%02x 0x%02x  ver: %u  cmd: %u  d.len: %u  data:",pkt->header[0], pkt->header[1], pkt->version, pkt->command, pkt->data_len);
}



void log_dp(tuya_data_point_t *dp)
{
    bool ok_dp = is_existing_dp(dp);

    switch (dp->data_type)
    {
    case TDT_INT:
        if (ok_dp) 
        {
        ESP_LOGI (TAG,"     DP  id: %02x %s  d.type: %s  d.len: %u  data: %02x %02x %02x %02x", 
                dp->dpid,
                dp_id_to_string((data_point_id_t)dp->dpid), 
                tuya_data_type_to_string((tuya_data_type_t)dp->data_type), dp->data_len, 
                *dp->data, *(dp->data+1), *(dp->data+2), *(dp->data+3));
        } else {
            ESP_LOGW (TAG,"     DP  id: %02x %s  d.type: %s  d.len: %u  data: %02x %02x %02x %02x", 
                dp->dpid,
                dp_id_to_string((data_point_id_t)dp->dpid), 
                tuya_data_type_to_string((tuya_data_type_t)dp->data_type), dp->data_len, 
                *dp->data, *(dp->data+1), *(dp->data+2), *(dp->data+3));
        }
        break;
    
    case TDT_BOOL:
        if (ok_dp) 
        {
        ESP_LOGI (TAG,"     DP  id: %02x %s  d.type: %s  d.len: %u  data: %02x", 
                dp->dpid,
                dp_id_to_string((data_point_id_t)dp->dpid), 
                tuya_data_type_to_string((tuya_data_type_t)dp->data_type), dp->data_len, 
                *dp->data);
        } else {
            ESP_LOGW (TAG,"     DP  id: %02x %s  d.type: %s  d.len: %u  data: %02x", 
                dp->dpid,
                dp_id_to_string((data_point_id_t)dp->dpid), 
                tuya_data_type_to_string((tuya_data_type_t)dp->data_type), dp->data_len, 
                *dp->data);
        }
        break;

    case TDT_STRING:
        {
        if (dp->data_len > 100) break;
        char buff[dp->data_len + 1] = {};
        memcpy (buff, dp->data, dp->data_len);
        if (ok_dp)
        {
            ESP_LOGI (TAG,"     DP  id: %02x %s  d.type: %s  d.len: %u  data: %s", 
                    dp->dpid,
                    dp_id_to_string((data_point_id_t)dp->dpid), 
                    tuya_data_type_to_string((tuya_data_type_t)dp->data_type), dp->data_len, 
                    buff);
        } else {
            ESP_LOGW (TAG,"     DP  id: %02x %s  d.type: %s  d.len: %u  data: %s", 
                dp->dpid,
                dp_id_to_string((data_point_id_t)dp->dpid), 
                tuya_data_type_to_string((tuya_data_type_t)dp->data_type), dp->data_len, 
                buff);
        }
        }
        break;
    
    default:
        ESP_LOGI (TAG,"     DP  id: %02x %s  d.type: %s  d.len: %u  data:", 
                dp->dpid,
                dp_id_to_string((data_point_id_t)dp->dpid), 
                tuya_data_type_to_string((tuya_data_type_t)dp->data_type), dp->data_len);
        ESP_LOG_BUFFER_HEXDUMP (TAG, dp->data, dp->data_type ==  TDT_INT ? 4 : dp->data_len, ok_dp ? ESP_LOG_INFO : ESP_LOG_WARN);
        break;
    }
}



const char *tuya_data_type_to_string (tuya_data_type_t type)
{
    switch (type)
    {
    case TDT_RAW: return "raw";
    case TDT_BOOL: return "bool";
    case TDT_INT: return "int";
    case TDT_STRING: return "string";
    case TDT_ENUM: return "enum";
    case TDT_BITMAP: return "bitmap";
    default: return "?";
    }
}



const char *pkt_error_to_string(parsing_errs_t err)
{
    switch (err)
    {
    case PCKT_NO_ERR: return "no err";
    case PCKT_ERR_WRONG_HEADER: return "wrong header";
    case PCKT_ERR_WRONG_LENGTH: return "wrong length";
    case PCKT_ERR_WRONG_CHECKSUM: return "wrong checksum";
    case PCKT_ERR: return "some err";
    
    default:
        return "?";
        break;
    }
}



void make_wifi_strength_packet(uint8_t *where, int8_t strength, uint16_t *out_size)
{
    where[0] = HEADER_0;
    where[1] = HEADER_1;
    where[2] = 0x00;  // version
    where[3] = TY_WIFI_POWER;
    where[4] = 0x00;  // data length
    where[5] = 0x01;  // data length
    where[6] = strength;   // per tuya protocol
    where[7] = pckt_check_sum(where, 7);

    *out_size = 8;
}



void make_network_status_pkt(uint8_t *where, tuya_network_status_t status, uint16_t *out_size)
{
    where[0] = HEADER_0;
    where[1] = HEADER_1;
    where[2] = 0x00;  // version
    where[3] = TY_NETWORK_STATUS;
    where[4] = 0x00;  // data length
    where[5] = 0x01;  // data length
    where[6] = status;
    where[7] = pckt_check_sum(where, 7);

    *out_size = 8;
}



void make_time_pkt(uint8_t *where, tuya_time_t *time, uint16_t *out_size)
{
    where[0] = HEADER_0;
    where[1] = HEADER_1;
    where[2] = 0x00;  // version
    where[3] = TY_LOCAL_TIME;
    where[4] = 0x00;  // data length
    where[5] = 0x08;  // data length
    memcpy (where + 6, time, sizeof(tuya_time_t));
    where[sizeof(tuya_time_t) + 6] = pckt_check_sum(where, sizeof(tuya_time_t) + 6);
    *out_size = sizeof(tuya_time_t) + 6 + 1;
}



void make_query_all_pkt(uint8_t *where, uint16_t *out_size)
{
    where[0] = HEADER_0;
    where[1] = HEADER_1;
    where[2] = 0x00;  // version
    where[3] = TY_QUERY_ALL;
    where[4] = 0x00;  // data length
    where[5] = 0x00;  // data length
    where[6] = 0x07; 

    *out_size = 7;
}



void make_product_info_pkt(uint8_t *where, uint16_t *out_size)
{
    where[0] = HEADER_0;
    where[1] = HEADER_1;
    where[2] = 0x00;  // version
    where[3] = TY_GET_PRODUCT_INFO;
    where[4] = 0x00;  // data length
    where[5] = 0x00;  // data length
    where[6] = 0x00;  // check sum

    *out_size = 7;
}



void make_heartbeat_pkt(uint8_t *where, uint16_t *out_size){

    where[0] = HEADER_0;
    where[1] = HEADER_1;
    where[2] = 0x00;  // version
    where[3] = TY_HEARTBEAT;
    where[4] = 0x00;  // data length
    where[5] = 0x00;  // data length
    where[6] = 0xFF;  // check sum

    *out_size = 7;
}



void make_working_timing_start_time_pkt (uint8_t *where, uint16_t *out_size)
{
    where[0] = HEADER_0;
    where[1] = HEADER_1;
    where[2] = 0x00;  // version
    where[3] = TY_SET;
    where[4] = 0x00;  // data length
    where[5] = 0x08;  // data length

    where[6] = DP_FAST_SMELL_START_TIME;
    where[7] = TDT_INT;   // data type int
    where[8] = 0x00;   // data length
    where[9] = 0x04;   // data length - big endian

    where[10] = 0x00;   // data
    where[11] = 0x00;   // data
    where[12] = 0x00;   // data
    where[13] = 0x00;   // data
    where[14] = pckt_check_sum(where, 14); 

    *out_size = 15;
}



void make_working_timing_end_time_pkt (uint8_t *where, uint16_t *out_size)
{
    where[0] = HEADER_0;
    where[1] = HEADER_1;
    where[2] = 0x00;  // version
    where[3] = TY_SET;
    where[4] = 0x00;  // data length
    where[5] = 0x08;  // data length

    where[6] = DP_WORKING_TIME_END;
    where[7] = TDT_INT;   // data type int
    where[8] = 0x00;   // data length
    where[9] = 0x04;   // data length - big endian

    where[10] = 0x00;   // data
    where[11] = 0x00;   // data
    where[12] = 0x00;   // data
    where[13] = 0x00;   // data
    where[14] = pckt_check_sum(where, 14); 

    *out_size = 15;
}



// 55 AA 00 06 00 1F 7F 03 00 1B   30 2C 30 2C 30 2C 30 2C 30 2C 30 2C 30 2C  31 2C 32 2C 33 2C 34 2C 35 2C 36 2C 37 B9    all weeks days   packet captured from tuya
// for some reason has bunch of 00000 symbols (looks like error)
void make_working_timing_all_week_days_pkt (uint8_t *where, uint16_t *out_size)
{
    where[0] = HEADER_0;
    where[1] = HEADER_1;
    where[2] = 0x00;  // version
    where[3] = TY_SET;
    where[4] = 0x00;  // data length
    where[5] = 0x08;  // data length

    where[6] = DP_FAST_SMELL_REPEAT;
    where[7] = TDT_STRING;   // data type int
    where[8] = 0x00;   // data length
    where[9] = 0x1D;   // data length - big endian

    where[10] = 0x31;   // data
    where[11] = 0x2C;   // data
    where[12] = 0x32;   // data
    where[13] = 0x2C;   // data
    where[14] = 0x33;   // data
    where[15] = 0x2C;   // data
    where[16] = 0x34;   // data
    where[17] = 0x2C;   // data
    where[18] = 0x35;   // data
    where[19] = 0x2C;   // data
    where[20] = 0x36;   // data
    where[21] = 0x2C;   // data
    where[22] = 0x37;   // data

    where[23] = pckt_check_sum(where, 23); 

    *out_size = 24;
}



bool is_used_dp(tuya_data_point_t *dp)
{
    switch (dp->dpid)
    {
        case DP_MOD:
        case DP_AREA:
        case DP_OIL_LEVEL:
        case DP_CONCENTRATION:
        case DP_SLEEP_TIME_START:
        case DP_SLEEP_TIME_END	:
        case DP_SCENARIO:
        case DP_FAST_SMELL:
        case DP_FAST_SMELL_TIME:
        case DP_FAST_SMELL_START_TIME:
        case DP_WORKING_TIME_END:
        case DP_FAST_SMELL_CTRL:
        case DP_FAST_SMELL_REPEAT:
        case DP_DEVICE_STATE:
        case DP_POSTPONE_START:
        case DP_WORK_COUNT:
        case DP_WORK_TIME_CD:
        case DP_CLEAR_DATA:
        case DP_CLEAN:
        case DP_POWER_SAVE_MODE:
        case DP_SET_WIFI_SSID:
        case DP_SET_WIFI_PSWD:
        case DP_OTA_STATE:
        case DP_FIRM_VER:
        return true;

        case DP_SEND_TIME_STAMP:
        case DP_TOTAL_YEAR:
        case DP_TOTAL_MONTH:
        case DP_TOTAL_WEEK:
        case DP_SCENE_0:
        case DP_SCENE_1:
        case DP_SCENE_2:
        case DP_SCENE_3:
        case DP_SCENE_4:
        case DP_SCENE_5:
        case DP_LOCATION:
        case DP_BETTER:
        case DP_WEEK7:
        case DP_DAY_TOTAL:
        case DP_MONTH10:
        case DP_MONTH20:
        case DP_MONTH31:
        case DP_YEAR1:
        case DP_YEAR2:
        case DP_YEAR3:
        return false;
    }
    return false;
}

bool is_existing_dp(tuya_data_point_t *dp)
{
    switch (dp->dpid)
    {
        case DP_MOD:
        case DP_AREA:
        case DP_OIL_LEVEL:
        case DP_CONCENTRATION:
        case DP_SLEEP_TIME_START:
        case DP_SLEEP_TIME_END	:
        case DP_SCENARIO:
        case DP_FAST_SMELL:
        case DP_FAST_SMELL_TIME:
        case DP_FAST_SMELL_START_TIME:
        case DP_WORKING_TIME_END:
        case DP_FAST_SMELL_CTRL:
        case DP_FAST_SMELL_REPEAT:
        case DP_DEVICE_STATE:
        case DP_POSTPONE_START:
        case DP_WORK_COUNT:
        case DP_WORK_TIME_CD:
        case DP_CLEAR_DATA:
        case DP_CLEAN:
        case DP_POWER_SAVE_MODE:
        case DP_SET_WIFI_SSID:
        case DP_SET_WIFI_PSWD:
        case DP_OTA_STATE:
        case DP_FIRM_VER:
        case DP_SEND_TIME_STAMP:
        case DP_TOTAL_YEAR:
        case DP_TOTAL_MONTH:
        case DP_TOTAL_WEEK:
        case DP_SCENE_0:
        case DP_SCENE_1:
        case DP_SCENE_2:
        case DP_SCENE_3:
        case DP_SCENE_4:
        case DP_SCENE_5:
        case DP_LOCATION:
        case DP_BETTER:
        case DP_WEEK7:
        case DP_DAY_TOTAL:
        case DP_MONTH10:
        case DP_MONTH20:
        case DP_MONTH31:
        case DP_YEAR1:
        case DP_YEAR2:
        case DP_YEAR3:
        return true;
    }

    return false;
}



void make_set_mode_dp(tuya_data_point_t *dp, bool mode)
{
    static uint8_t dp_data[1] = {0};

    dp->dpid = DP_MOD;
    dp->data_type = TDT_BOOL;
    dp->data_len = 1;
    dp->data = dp_data;

    dp_data[0] = (uint8_t)mode;
}



void make_fung_ota_set_wifi_psw_packet(uint8_t *where, uint16_t *out_size, char *wifi_psw)
{
    if (strlen(wifi_psw) > 16)
    {
        ESP_LOGE (TAG, "wifi pws too long");
        *out_size = 0;
        return;
    }

    where[0] = HEADER_0;
    where[1] = HEADER_1;
    where[2] = 0x00;  // version
    where[3] = TY_SET;
    where[4] = 0x00;  // data length. see below
    where[5] = 0x00;  // data length. see below

    tuya_data_point_t *dp = (tuya_data_point_t *)&where[6];

    dp->dpid = DP_SET_WIFI_PSWD;
    dp->data_type = TDT_STRING;
    dp->data_len = strlen(wifi_psw)/256 + strlen(wifi_psw)%256*256;
    memcpy (&(dp->data), wifi_psw, strlen(wifi_psw));

    where[5] = (4 + strlen(wifi_psw)) % 256;
    where[4] = (4 + strlen(wifi_psw)) / 256;
    
    uint8_t *checksum_pos = (uint8_t *)dp + 1 + 1 + 2 + strlen(wifi_psw);
    *checksum_pos = pckt_check_sum (where, checksum_pos - where);  // check sum

    *out_size = checksum_pos - where + 1;
}



void make_fung_ota_set_wifi_ssid_packet(uint8_t *where, uint16_t *out_size, char *wifi_ssid)
{
    if (strlen(wifi_ssid) > 16)
    {
        ESP_LOGE (TAG, "wifi ssid too long");
        *out_size = 0;
        return;
    }

    where[0] = HEADER_0;
    where[1] = HEADER_1;
    where[2] = 0x00;  // version
    where[3] = TY_SET;
    where[4] = 0x00;  // data length. see below
    where[5] = 0x00;  // data length. see below

    tuya_data_point_t *dp = (tuya_data_point_t *)&where[6];

    dp->dpid = DP_SET_WIFI_SSID;
    dp->data_type = TDT_STRING;
    dp->data_len = strlen(wifi_ssid)/256 + strlen(wifi_ssid)%256*256;
    memcpy (&(dp->data), wifi_ssid, strlen(wifi_ssid));

    where[5] = (4 + strlen(wifi_ssid)) % 256;
    where[4] = (4 + strlen(wifi_ssid)) / 256;
    
    uint8_t *checksum_pos = (uint8_t *)dp + 1 + 1 + 2 + strlen(wifi_ssid);
    *checksum_pos = pckt_check_sum (where, checksum_pos - where);  // check sum

    *out_size = checksum_pos - where + 1;
}



void make_fung_ota_start_packet(uint8_t *where, uint16_t *out_size)
{
    where[0] = HEADER_0;
    where[1] = HEADER_1;
    where[2] = 0x00;  // version
    where[3] = TY_SET;
    where[4] = 0x00;  
    where[5] = 0x08;  

    tuya_data_point_t *dp = (tuya_data_point_t *)&where[6];
    dp->dpid = DP_OTA_STATE;
    dp->data_type = TDT_INT;
    dp->data_len = 4 * 256;  // 256 coz in big endian
    uint32_t status = 0x01000000;
    memcpy (&(dp->data), &status, 4);

    where[14] = pckt_check_sum (where, 14);
    *out_size = 15;
}



/*===============================================*\
 * Local function definitions
\*===============================================*/
static void add_two_bytes_in_big_endian(uint8_t *add_to, const uint8_t *in)
{
    *add_to = *add_to + *in + ((*(add_to + 1) + *(in + 1)) /256);
    *(add_to+1) = (*(add_to + 1) + *(in + 1)) % 256;
}



static bool check_dp_id_matches_its_data_type (data_point_id_t id, tuya_data_type_t type)
{
    tuya_data_point_t dp = {};
    dp.dpid = id;
    if (!is_used_dp(&dp)) return true;

    switch (id)
    {
        // bool
    case DP_MOD:   
    case DP_FAST_SMELL_CTRL:
        if(type == TDT_BOOL) 
        {
            return true; 
        } else 
        {
            return false;
        }

        break;

        // int
    case DP_POWER_SAVE_MODE:
    case DP_AREA:
    case DP_OIL_LEVEL:
    case DP_CONCENTRATION:
    case DP_SLEEP_TIME_START:
    case DP_SLEEP_TIME_END:
    case DP_SCENARIO:
    case DP_FAST_SMELL:
    case DP_FAST_SMELL_TIME:
    case DP_FAST_SMELL_START_TIME:
    case DP_WORKING_TIME_END:
    case DP_DEVICE_STATE:
    case DP_POSTPONE_START:
    case DP_WORK_COUNT:
    case DP_WORK_TIME_CD:
    case DP_CLEAR_DATA:
    case DP_CLEAN:
    case DP_OTA_STATE:
        if(type == TDT_INT) 
        {
            return true; 
        } else 
        {
            return false;
        }
        break;

    // string
    case DP_FAST_SMELL_REPEAT:
    case DP_SET_WIFI_SSID:
    case DP_SET_WIFI_PSWD:
    case DP_FIRM_VER:
        if(type == TDT_STRING) 
        {
            return true; 
        } else 
        {
            return false;
        }
    default:
        break;
    }

    return false;

}


} // a830::uart_protocol namespace