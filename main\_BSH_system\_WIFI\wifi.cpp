#include <stdint.h>
#include <stdbool.h>

#include "string.h"
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event_loop.h"
#include "esp_log.h"
#include "esp_ota_ops.h"
#include "esp_https_ota.h"
#include "esp_timer.h"

#include "wifi.h"



#define DONT_RECONNECT_IF_CREDS_ARE_SAME 0
#define NO_IP_TIMEOUT 5000 // ms


namespace bsh_sys::wifi {


ESP_EVENT_DEFINE_BASE(WIFI_EVENTS);

enum {
    C_CONNECTING,
    C_WAITING_TO_CONNECT,
    C_CONNECTED,
    C_DISCONNECTED,
} connection_status_t;


/* ======= local object declarations =========*/
static const char *TAG = "- wifi -";
static wifi_config_t wifi_config;
static int s_retry_num = 0;
static uint8_t connecting_state = C_DISCONNECTED;
static esp_event_loop_handle_t loop_hdl;
static void(* rssi_callback)(int8_t rssi) = NULL;
static esp_timer_handle_t rssi_scan_timer = NULL;
static esp_timer_handle_t reconnect_timer = NULL;
static esp_timer_handle_t no_ip_from_DHCP_timer = NULL;
static bool no_events = false;



/* ======= local function declarations ========= */
static void event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data);
static const char * wifi_event_to_string(int32_t event_id);
static void copy_wifi_config(bsh_wifi_settings_t *settings);
static void rssi_timer_cb(void *);
static void reconnect_timer_cb(void* arg);
static void no_ip_from_DHCP_cb(void* arg);
static bool check_wifi_config(bsh_wifi_settings_t *settings);
static const char * wifi_disconnect_reason_to_string (uint8_t reason);
static void connect_wrapper();



/*=============================================================
 * External functions definition
 =============================================================*/
esp_err_t start_and_connect(bsh_wifi_settings_t *settings)
{
    static bool initilized = false;
    ESP_LOGI(TAG,"start and connect called. current state: %u", connecting_state);

    if (!check_wifi_config(settings)) 
    {
        ESP_LOGE(TAG,"wrong wifi args");
        return ESP_ERR_INVALID_ARG;
    }
    copy_wifi_config(settings);
    no_events = false;

    #if DONT_RECONNECT_IF_CREDS_ARE_SAME == 1
    if(memcmp(settings->ssid, wifi_config.sta.ssid, strlen(settings->ssid))==0 && 
           memcmp(settings->ssid, wifi_config.sta.passw, strlen(settings->passw))==0 && 
        connecting_state == C_CONNECTED)
    {
        ESP_LOGW(TAG,"creds same. no reconnect");
        return;
    }
    #endif



    if(!initilized)
    {
        initilized = true;

        // reconnect timer
        esp_timer_create_args_t timerArgs = {};
        timerArgs.callback = reconnect_timer_cb;
        ESP_ERROR_CHECK(esp_timer_create(&timerArgs, &reconnect_timer));

        esp_timer_create_args_t timer2Args = {};
        timer2Args.callback = no_ip_from_DHCP_cb;
        ESP_ERROR_CHECK(esp_timer_create(&timer2Args, &no_ip_from_DHCP_timer));

        ESP_ERROR_CHECK(esp_netif_init());

        ESP_ERROR_CHECK(esp_event_loop_create_default());
        esp_netif_create_default_wifi_sta();

        wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
        ESP_ERROR_CHECK(esp_wifi_init(&cfg));

        esp_event_handler_instance_t instance_any_id;
        esp_event_handler_instance_t instance_got_ip;
        ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT,
                                                            ESP_EVENT_ANY_ID,
                                                            &event_handler,
                                                            NULL,
                                                            &instance_any_id));
        ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT,
                                                            IP_EVENT_STA_GOT_IP,
                                                            &event_handler,
                                                            NULL,
                                                            &instance_got_ip));
        // wifi_config.sta.threshold.authmode = WIFI_AUTH_WPA2_PSK;
        wifi_config.sta.pmf_cfg.capable = true;
        wifi_config.sta.pmf_cfg.required = false;
        wifi_config.sta.scan_method = WIFI_ALL_CHANNEL_SCAN;
        wifi_config.sta.sort_method = WIFI_CONNECT_AP_BY_SIGNAL;
        
        ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA) );
        ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
        ESP_ERROR_CHECK(esp_wifi_start());

        ESP_LOGI(TAG, "wifi_init_sta finished.");

    } else {
        switch (connecting_state)
        {
        case C_CONNECTED:
            connecting_state = C_WAITING_TO_CONNECT;
            esp_timer_start_once(reconnect_timer, RECONNECT_TIME*1000000);
            esp_wifi_disconnect();
            s_retry_num = 0;
            break;
        case C_DISCONNECTED:
            s_retry_num = 0;
            connect_wrapper();
            break;
        case C_WAITING_TO_CONNECT:
            // do nothing. but copy creds when ready to connect
            break;
        case C_CONNECTING:
            esp_timer_stop(reconnect_timer);
            connecting_state = C_WAITING_TO_CONNECT;
            esp_timer_start_once(reconnect_timer, RECONNECT_TIME*1000000);
            break;
        }
    }
    return ESP_OK;
}



void stop_reconnecting()
{
    ESP_LOGI(TAG,"stop reconn called");
    esp_timer_stop(reconnect_timer);
    no_events = true;
}



bool is_connected()
{
    return connecting_state == C_CONNECTED;
}



int8_t get_rssi()
{
    if (connecting_state != C_CONNECTED) return 0;

    wifi_ap_record_t ap_rec;
    esp_wifi_sta_get_ap_info(&ap_rec);
    return ap_rec.rssi;
}



void send_rssi_regulary (void(* rssi_cb)(int8_t rssi), uint16_t period_seconds)
{
    if (rssi_cb == NULL) 
    {
        ESP_LOGE (TAG,"null pointer provided");
        return;
    }

    rssi_callback = rssi_cb;

    if (rssi_scan_timer == NULL)
    {
        const esp_timer_create_args_t timerArgs = {rssi_timer_cb, 0, {}, 0, 0};
        ESP_ERROR_CHECK(esp_timer_create(&timerArgs, &rssi_scan_timer));
    }

    esp_timer_start_periodic(rssi_scan_timer, period_seconds * 1000000);
}



void wifi_set_power_saving_mode(wifi_ps_type_t type)
{
    ESP_ERROR_CHECK(esp_wifi_set_ps(type));
}



/*=============================================================
 * Local functions definition
 =============================================================*/
static void event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data)
{
    ESP_LOGI(TAG,"event %li %s.  current state: %i",event_id, wifi_event_to_string(event_id), connecting_state);

    if(event_base == WIFI_EVENT){
        switch (event_id)
        {
        case WIFI_EVENT_STA_START:
            connect_wrapper();
            break;
        
        case WIFI_EVENT_STA_DISCONNECTED:
            esp_timer_stop(no_ip_from_DHCP_timer);
            ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
            switch (connecting_state)
            {
            case C_CONNECTED:
                connecting_state = C_DISCONNECTED;
                esp_timer_start_once(reconnect_timer, RECONNECT_TIME *100000);
                break;  

            case C_WAITING_TO_CONNECT:
                ESP_LOGW(TAG, " == reconnecting == ");
                connecting_state = C_CONNECTING;
                s_retry_num = 0;
                // ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
                connect_wrapper();
                break;

            case C_DISCONNECTED:
                esp_timer_start_once(reconnect_timer, 2 *1000000);
                break;

            case C_CONNECTING:
                if (s_retry_num < 3) {
                    s_retry_num++;
                    ESP_LOGW(TAG, "connect retry N %i",s_retry_num);
                    connect_wrapper();
                } else {
                    ESP_LOGW(TAG, "WIFI disconnected");
                    connecting_state = C_DISCONNECTED;

                    wifi_event_sta_disconnected_t *disconnect_data = (wifi_event_sta_disconnected_t *) event_data;
                    ESP_LOGW(TAG, "disconnect reason: %i  %s", disconnect_data->reason, wifi_disconnect_reason_to_string(disconnect_data->reason));
                    
                    if (disconnect_data->reason == WIFI_REASON_NO_AP_FOUND) 
                    {
                        if(!no_events) esp_event_post_to(loop_hdl, WIFI_EVENTS, WIFI_CONN_ERR_NETWORK_NOT_FOUND, NULL, 0, 0);
                        esp_timer_start_once(reconnect_timer, RECONNECT_TIME *1000000);
                    } else if (disconnect_data->reason == WIFI_REASON_CONNECTION_FAIL || disconnect_data->reason == WIFI_REASON_AUTH_FAIL ||
                                disconnect_data->reason == WIFI_REASON_4WAY_HANDSHAKE_TIMEOUT || disconnect_data->reason == WIFI_REASON_HANDSHAKE_TIMEOUT)  { 

                        esp_timer_stop(reconnect_timer);
                        if(!no_events) esp_event_post_to(loop_hdl, WIFI_EVENTS, WIFI_CONN_ERR_WRONG_PASSW, NULL, 0, 0);
                    } else {
                        if(!no_events) esp_event_post_to(loop_hdl, WIFI_EVENTS, WIFI_DISCONNECTED, NULL, 0, 0);
                        esp_timer_start_once(reconnect_timer, RECONNECT_TIME *1000000);
                    }
                }                
                break;
            }
            break;

            case WIFI_EVENT_STA_CONNECTED:
                ESP_LOGI(TAG,"wifi connected - requesting for address");
                esp_timer_start_once(no_ip_from_DHCP_timer, NO_IP_TIMEOUT*1000);
                break;
        } 
    } else if (event_base == IP_EVENT) {
        if(event_id == IP_EVENT_STA_GOT_IP)
        {
            connecting_state = C_CONNECTED;
            esp_timer_stop(no_ip_from_DHCP_timer);
            ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
            ESP_LOGI(TAG, "\n======================\n WIFI connected.  ip: %d.%d.%d.%d \n======================", IP2STR(&event->ip_info.ip));
            s_retry_num = 0;
            rssi_timer_cb(NULL);  // to send first rssi right away, not after period
            if(!no_events) esp_event_post_to(loop_hdl, WIFI_EVENTS, WIFI_CONNECTED, NULL, 0, 0);
            esp_timer_stop(reconnect_timer);
        }
    }
}



static void reconnect_timer_cb(void* arg)
{
    if (connecting_state == C_CONNECTED) return;
    
    ESP_LOGI (TAG,"reconnect timer called");
    if (connecting_state == C_DISCONNECTED)
    {
        connecting_state = C_CONNECTING;
        connect_wrapper();
    } else {
        connecting_state = C_WAITING_TO_CONNECT;
        esp_timer_start_once(reconnect_timer, RECONNECT_TIME*1000000);
    }
    s_retry_num = 0;
}



static void no_ip_from_DHCP_cb(void* arg)
{
    ESP_LOGW(TAG,"cant get IP address");
    esp_event_post_to(loop_hdl, WIFI_EVENTS, WIFI_CONN_ERR_NO_IP, NULL, 0, 0);
    esp_wifi_disconnect();
}



static void connect_wrapper()
{
    esp_timer_stop(no_ip_from_DHCP_timer);
    if (esp_wifi_connect() != ESP_OK)
    {
        ESP_LOGE(TAG,"error calling wifi connect");
    }

}



static bool check_wifi_config(bsh_wifi_settings_t *settings)
{
    if (settings == NULL || settings->ssid == NULL || settings->passw == NULL) 
    {
        ESP_LOGE (TAG, "wrong input settings");
        return false;
    }

    if (strlen(settings->ssid) > 32)
    {
        ESP_LOGE(TAG, "ssid is too big: >32");
        return false;
    }

    if (strlen(settings->passw) > 64)
    {
        ESP_LOGE(TAG, "pswd is too big: >64");
        return false;
    }
    return true;
}



static void copy_wifi_config(bsh_wifi_settings_t *settings)
{
    
    strcpy ((char *)wifi_config.sta.ssid, settings->ssid);
    ESP_LOGI(TAG, "ssid set to: %s", wifi_config.sta.ssid);


    strcpy ((char *)wifi_config.sta.password, settings->passw);
    ESP_LOGI (TAG, "passw set to %s", wifi_config.sta.password);

    loop_hdl = settings->loop_handle;
}



static const char * wifi_disconnect_reason_to_string (uint8_t reason)   // see wifi_err_reason_t in wifi lib
{
    switch (reason)
    {

    case WIFI_REASON_UNSPECIFIED             : return "unspecified";
    case WIFI_REASON_AUTH_EXPIRE             : return "";
    case WIFI_REASON_AUTH_LEAVE              : return "";
    case WIFI_REASON_ASSOC_EXPIRE            : return "";
    case WIFI_REASON_ASSOC_TOOMANY           : return "";
    case WIFI_REASON_NOT_AUTHED              : return "";
    case WIFI_REASON_NOT_ASSOCED             : return "";
    case WIFI_REASON_ASSOC_LEAVE             : return "assoc leave";
    case WIFI_REASON_ASSOC_NOT_AUTHED        : return "";
    case WIFI_REASON_DISASSOC_PWRCAP_BAD     : return "";
    case WIFI_REASON_DISASSOC_SUPCHAN_BAD    : return "";
    case WIFI_REASON_BSS_TRANSITION_DISASSOC : return "";
    case WIFI_REASON_IE_INVALID              : return "";
    case WIFI_REASON_MIC_FAILURE             : return "";
    case WIFI_REASON_4WAY_HANDSHAKE_TIMEOUT  : return "handshake timeout";
    case WIFI_REASON_GROUP_KEY_UPDATE_TIMEOUT: return "";
    case WIFI_REASON_IE_IN_4WAY_DIFFERS      : return "";
    case WIFI_REASON_GROUP_CIPHER_INVALID    : return "";
    case WIFI_REASON_PAIRWISE_CIPHER_INVALID : return "";
    case WIFI_REASON_AKMP_INVALID            : return "";
    case WIFI_REASON_UNSUPP_RSN_IE_VERSION   : return "";
    case WIFI_REASON_INVALID_RSN_IE_CAP      : return "";
    case WIFI_REASON_802_1X_AUTH_FAILED      : return "802 auth failed";
    case WIFI_REASON_CIPHER_SUITE_REJECTED   : return "cipher suite err";
    case WIFI_REASON_INVALID_PMKID           : return "";
    case WIFI_REASON_BEACON_TIMEOUT          : return "";
    case WIFI_REASON_NO_AP_FOUND             : return "no network";
    case WIFI_REASON_AUTH_FAIL               : return "auth fail";
    case WIFI_REASON_ASSOC_FAIL              : return "";
    case WIFI_REASON_HANDSHAKE_TIMEOUT       : return "conn fail";
    case WIFI_REASON_CONNECTION_FAIL         : return "wrong passw";
    case WIFI_REASON_AP_TSF_RESET            : return "";
    case WIFI_REASON_ROAMING                 : return "";

    default: return "unknown";
        break;
    }
}



static void rssi_timer_cb(void *)
{
    rssi_callback(get_rssi());
}


static const char * wifi_event_to_string(int32_t event_id)
{
    switch (event_id)
    {
    case WIFI_EVENT_WIFI_READY:            return "WiFi connected";
    case WIFI_EVENT_SCAN_DONE:             return "finish scanning";
    case WIFI_EVENT_STA_START:             return "station start";
    case WIFI_EVENT_STA_STOP:              return "station stop";
    case WIFI_EVENT_STA_CONNECTED:         return "connected";
    case WIFI_EVENT_STA_DISCONNECTED:      return "disconnected";
    case WIFI_EVENT_STA_AUTHMODE_CHANGE:   return "auth mode changed";

    case WIFI_EVENT_STA_WPS_ER_SUCCESS:     return "wps succeeds";
    case WIFI_EVENT_STA_WPS_ER_FAILED:      return "wps fails";
    case WIFI_EVENT_STA_WPS_ER_TIMEOUT:     return "wps timeout";
    case WIFI_EVENT_STA_WPS_ER_PIN:         return "wps pin code";
    case WIFI_EVENT_STA_WPS_ER_PBC_OVERLAP: return "wps overlap";
    
    case WIFI_EVENT_AP_START:               return "soft-AP start";
    case WIFI_EVENT_AP_STOP:                return "soft-AP stop";
    case WIFI_EVENT_AP_STACONNECTED:        return "connected to ESP";
    case WIFI_EVENT_AP_STADISCONNECTED:     return "disconnected from ESP";
    case WIFI_EVENT_AP_PROBEREQRECVED:      return "probe request";

    case WIFI_EVENT_FTM_REPORT:             return "report of FTM";
    case WIFI_EVENT_STA_BSS_RSSI_LOW:       return "RSSI crossed thresh";
    case WIFI_EVENT_ACTION_TX_STATUS:       return "Action Tx";
    case WIFI_EVENT_ROC_DONE:               return "operation complete";
    case WIFI_EVENT_STA_BEACON_TIMEOUT:     return  "beacon timeout";
    case WIFI_EVENT_MAX:                    return  "?";

    default:                                return "??";
    }
}



}  // bsh_sys::wifi namespace