#pragma once

#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "uart_protocol.h"
#include "mqtt_protocol.h"
#include "system.h"

namespace a630::main_task::queue_helpers {


/** 
 * initialises queues. sizes defined in device_specific_data.h
 */
void init();



/************************
 *
 *    UART commands
 * 
 ***********************/    

typedef enum {
    UART_CMD_QUERY_ALL,
    UART_CMD_TIME,
    UART_CMD_NETWORK_STATUS,
    UART_CMD_START_FNG_OTA,   // sends three packets in a row: set wifi creds and start ota
    UART_CMD_REQUEST_PROD_INFO,
    UART_CMD_SEND_RSSI,
    UART_CMD_SEND_HEARTBEAT,
} uart_commands_t;



/** 
 * Returns uart out queue handle
 */
QueueHandle_t get_uart_queue();



/**
 * Sends commands to uart.
 * !!! puts BIG uart_out_obj on stack !!! consider other big things on stack at same time
 */
void send_uart_command(uart_commands_t command);



/** 
 * Send working timing settings to uart: to make it "always on" - coz these timing feature is not implemented in mobile app
 */
void send_working_time_always_on ();



/**
 * Sends array of DPs to uart
 * !!! puts BIG uart_out_obj on stack !!! consider other big things on stack at same time
 */
void send_DPs_to_uart(a630::uart_protocol::tuya_data_point_t *dps, uint8_t dps_qty);



// helpers for helpers.
void time_from_system_to_tuya(const bsh_sys::date_time::date_and_time_t *sys_time, a630::uart_protocol::tuya_time_t *out_tuya_time);
a630::uart_protocol::tuya_network_status_t sys_ntw_status_to_tuya_format(bsh_sys::smart_home_conn_events_t status);



} // a630::main_task::queue_helpers