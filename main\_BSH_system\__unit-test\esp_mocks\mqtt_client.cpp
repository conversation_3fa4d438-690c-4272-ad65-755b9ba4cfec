#include "mqtt_client.h"



esp_mqtt_client_handle_t esp_mqtt_client_init(const esp_mqtt_client_config_t *config)
{

};

esp_err_t esp_mqtt_client_start(esp_mqtt_client_handle_t client)
{

};

esp_err_t esp_mqtt_set_config(esp_mqtt_client_handle_t client, const esp_mqtt_client_config_t *config)
{

};

int esp_mqtt_client_subscribe(esp_mqtt_client_handle_t client, const char *topic, int qos)
{

};

esp_err_t esp_mqtt_client_reconnect(esp_mqtt_client_handle_t client)
{

};

esp_err_t esp_mqtt_client_disconnect(esp_mqtt_client_handle_t client)
{

};

esp_err_t esp_mqtt_client_stop(esp_mqtt_client_handle_t client)
{

};

int esp_mqtt_client_publish(esp_mqtt_client_handle_t client, const char *topic, const char *data, int len, int qos, int retain)
{

};

esp_err_t esp_mqtt_client_register_event(esp_mqtt_client_handle_t client,
                                         esp_mqtt_event_id_t event,
                                         esp_event_handler_t event_handler,
                                         void *event_handler_arg)

                                         {

                                         };
